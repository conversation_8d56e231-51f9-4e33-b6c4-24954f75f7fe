<?php

namespace App\Http\Controllers\Marketplace\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\MarketOrder;
use App\Models\MarketOrderDetail;
use App\Models\User;
use App\Models\Customer;
use App\Services\Marketplace\MarketplaceMailService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;
use Dompdf\Options;

class OrderController extends Controller
{
    public function index(Request $request)
    {
        $filters = $request->only(['search', 'status', 'payment_status', 'from_date', 'to_date', 'sort', 'direction']);

        $query = MarketOrder::with(['user', 'customer', 'orderDetails', 'payments'])
            ->when($filters['search'] ?? null, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('order_number', 'like', "%{$search}%")
                      ->orWhere('customer_name', 'like', "%{$search}%")
                      ->orWhere('customer_phone', 'like', "%{$search}%")
                      ->orWhere('customer_email', 'like', "%{$search}%");
                });
            })
            ->when($filters['status'] ?? null, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($filters['payment_status'] ?? null, function ($query, $paymentStatus) {
                $query->where('payment_status', $paymentStatus);
            })
            ->when($filters['from_date'] ?? null, function ($query, $fromDate) {
                $query->whereDate('created_at', '>=', $fromDate);
            })
            ->when($filters['to_date'] ?? null, function ($query, $toDate) {
                $query->whereDate('created_at', '<=', $toDate);
            });


        $sortField = $filters['sort'] ?? 'created_at';
        $sortDirection = $filters['direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $orders = $query->paginate(15)->withQueryString();


        $stats = $this->getOrderStatistics();

        return Inertia::render('Marketplace/Orders/Index', [
            'orders' => $orders,
            'filters' => $filters,
            'stats' => $stats,
            'statuses' => $this->getOrderStatuses(),
            'paymentStatuses' => $this->getPaymentStatuses(),
        ]);
    }

    public function show(MarketOrder $order)
    {
        $order->load([
            'user',
            'customer',
            'orderDetails.product',
            'payments'
        ]);

        return Inertia::render('Marketplace/Orders/Show', [
            'order' => $order
        ]);
    }

    public function updateStatus(Request $request, MarketOrder $order)
    {
        $request->validate([
            'status' => 'required|string|in:pending,confirmed,processing,shipping,completed,cancelled,refunded',
            'notes' => 'nullable|string|max:500'
        ]);

        $oldStatus = $order->status;
        $newStatus = (string) $request->input('status');

        DB::transaction(function () use ($order, $request, $oldStatus, $newStatus) {
            $updateData = [
                'status' => $newStatus,
                'updated_at' => now()
            ];
            if ($request->filled('notes')) {
                $updateData['notes'] = $request->input('notes');
            }
            $order->update($updateData);

            if ($newStatus === 'shipping' && $oldStatus !== 'shipping') {
                $order->update(['shipped_at' => now()]);
            } elseif ($newStatus === 'completed' && $oldStatus !== 'completed') {
                $order->update(['delivered_at' => now()]);
            }
        });


        try {
            $cancelReason = ($newStatus === 'cancelled') ? $request->input('notes') : null;
            MarketplaceMailService::sendOrderStatusUpdate($order, $oldStatus, $cancelReason);
        } catch (\Exception $e) {
            Log::warning('Failed to send order status update email', [
                'order_id' => $order->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);
        }

        return redirect()->back()->with('success', 'Trạng thái đơn hàng đã được cập nhật thành công.');
    }

    public function export(Request $request)
    {
        $format = $request->input('format', 'excel');


        $query = MarketOrder::with(['user', 'customer', 'orderDetails.product', 'payments']);


        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }


        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }


        if ($request->has('payment_status') && !empty($request->payment_status)) {
            $query->where('payment_status', $request->payment_status);
        }


        if ($request->has('from_date') && !empty($request->from_date)) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }

        if ($request->has('to_date') && !empty($request->to_date)) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }


        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);


        $orders = $query->get();


        $exportData = $orders->map(function ($order) {
            return [
                'Mã đơn hàng' => $order->order_number,
                'Khách hàng' => $order->customer_name,
                'Số điện thoại' => $order->customer_phone ?: '-',
                'Email' => $order->customer_email ?: '-',
                'Địa chỉ giao hàng' => $order->shipping_address ?: '-',
                'Tổng phụ' => number_format($order->subtotal, 0, ',', '.') . ' ₫',
                'Phí vận chuyển' => number_format($order->shipping_fee, 0, ',', '.') . ' ₫',
                'Giảm giá' => number_format($order->discount_amount, 0, ',', '.') . ' ₫',
                'Thành tiền' => number_format($order->total_amount, 0, ',', '.') . ' ₫',
                'Trạng thái' => $this->getStatusLabel($order->status),
                'Trạng thái thanh toán' => $this->getPaymentStatusLabel($order->payment_status),
                'Ngày tạo' => $order->created_at->format('d/m/Y H:i'),
                'Ngày giao hàng' => $order->delivered_at ? $order->delivered_at->format('d/m/Y H:i') : '-',
                'Ghi chú' => $order->notes ?: '-',
            ];
        });


        $filename = 'orders_export';
        if ($request->has('from_date') && $request->has('to_date')) {
            $filename .= '_' . $request->from_date . '_to_' . $request->to_date;
        } else {
            $filename .= '_' . now()->format('Y-m-d');
        }


        if ($format === 'pdf') {
            return $this->exportPDF($exportData, $filename);
        } else {
            return $this->exportExcel($exportData, $filename);
        }
    }

    /**
     * Export data to Excel
     */
    private function exportExcel($data, $filename)
    {

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();


        if ($data->count() > 0) {
            $headers = array_keys($data->first());
            $columnIndex = 1;
            foreach ($headers as $header) {
                $sheet->setCellValue([$columnIndex, 1], $header);
                $columnIndex++;
            }


            $headerRange = 'A1:' . chr(64 + count($headers)) . '1';
            $sheet->getStyle($headerRange)->getFont()->setBold(true);
            $sheet->getStyle($headerRange)->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setARGB('FFE2E8F0');
        }


        if ($data->count() > 0) {
            $rowIndex = 2;
            foreach ($data as $row) {
                $columnIndex = 1;
                foreach ($row as $value) {
                    $sheet->setCellValue([$columnIndex, $rowIndex], $value);
                    $columnIndex++;
                }
                $rowIndex++;
            }
        }


        foreach (range('A', chr(64 + count(array_keys($data->first())))) as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }


        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer->save($tempFile);


        return response()->download($tempFile, $filename . '.xlsx', [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Export data to PDF
     */
    private function exportPDF($data, $filename)
    {

        $columnCount = $data->count() > 0 ? count($data->first()) : 0;
        $columnWidths = $this->calculateColumnWidths($columnCount);


        $html = '<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>';
        $html .= '<style>
            body { font-family: DejaVu Sans, sans-serif; font-size: 10px; }
            .container { margin: 10px; }
            h1 { color: #333; text-align: center; margin-bottom: 15px; font-size: 16px; }
            table { width: 100%; border-collapse: collapse; margin-top: 10px; table-layout: fixed; }
            th, td { border: 1px solid #ddd; padding: 5px; text-align: left; font-size: 8px; overflow: hidden; word-wrap: break-word; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .text-right { text-align: right; }
            .text-center { text-align: center; }';


        foreach ($columnWidths as $index => $width) {
            $html .= '.col-' . $index . ' { width: ' . $width . '%; }';
        }

        $html .= '</style>';
        $html .= '</head><body>';

        $html .= '<div class="container">';
        $html .= '<h1>Báo cáo đơn hàng</h1>';


        $html .= '<p class="text-right">Ngày xuất: ' . date('d/m/Y H:i') . '</p>';

        $html .= '<table>';


        $columnClasses = [];
        if ($data->count() > 0) {
            $headers = array_keys($data->first());
            foreach ($headers as $index => $header) {

                $columnClasses[$index] = 'col-' . $index;
            }
        }


        if ($data->count() > 0) {
            $html .= '<thead><tr>';
            foreach (array_keys($data->first()) as $index => $header) {
                $class = $columnClasses[$index] ?? '';
                $html .= '<th class="' . $class . '">' . htmlspecialchars($header) . '</th>';
            }
            $html .= '</tr></thead>';
        }


        $html .= '<tbody>';
        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($row as $index => $value) {
                $class = $columnClasses[$index] ?? '';
                $html .= '<td class="' . $class . '">' . htmlspecialchars($value) . '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody>';

        $html .= '</table>';
        $html .= '</div>';
        $html .= '</body></html>';


        $options = new \Dompdf\Options();
        $options->set('defaultFont', 'DejaVu Sans');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', true);


        if ($columnCount > 8) {
            $options->set('defaultPaperSize', 'A3');
        } else {
            $options->set('defaultPaperSize', 'A4');
        }
        $options->set('defaultPaperOrientation', 'landscape');

        $dompdf = new \Dompdf\Dompdf($options);
        $dompdf->loadHtml($html);
        $dompdf->render();


        $pdfContent = $dompdf->output();
        $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');
        file_put_contents($tempFile, $pdfContent);


        return response()->download($tempFile, $filename . '.pdf', [
            'Content-Type' => 'application/pdf',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Calculate optimal column widths based on the number of columns
     *
     * @param int $columnCount
     * @return array
     */
    private function calculateColumnWidths($columnCount)
    {
        $widths = [];

        if ($columnCount <= 0) {
            return $widths;
        }


        $baseWidth = 100 / $columnCount;

        for ($i = 0; $i < $columnCount; $i++) {
            $widths[$i] = $baseWidth;
        }

        return $widths;
    }

    public function getOrderDetails(Request $request)
    {
        $orderId = $request->get('order_id');
        $order = MarketOrder::with([
            'user',
            'customer',
            'orderDetails.product',
            'payments'
        ])->findOrFail($orderId);

        return response()->json($order);
    }

    private function getOrderStatistics()
    {
        $today = now()->startOfDay();
        $thisMonth = now()->startOfMonth();

        return [
            'total_orders' => MarketOrder::count(),
            'pending_orders' => MarketOrder::where('status', 'pending')->count(),
            'processing_orders' => MarketOrder::whereIn('status', ['confirmed', 'processing'])->count(),
            'completed_orders' => MarketOrder::where('status', 'completed')->count(),
            'today_orders' => MarketOrder::whereDate('created_at', $today)->count(),
            'this_month_orders' => MarketOrder::whereDate('created_at', '>=', $thisMonth)->count(),
            'total_revenue' => MarketOrder::where('status', 'completed')->sum('total_amount'),
            'this_month_revenue' => MarketOrder::where('status', 'completed')
                ->whereDate('created_at', '>=', $thisMonth)
                ->sum('total_amount'),
        ];
    }

    private function getOrderStatuses()
    {
        return [
            ['value' => '', 'label' => 'Tất cả trạng thái'],
            ['value' => 'pending', 'label' => 'Chờ xử lý'],
            ['value' => 'confirmed', 'label' => 'Đã xác nhận'],
            ['value' => 'processing', 'label' => 'Đang xử lý'],
            ['value' => 'shipping', 'label' => 'Đang giao hàng'],
            ['value' => 'completed', 'label' => 'Hoàn thành'],
            ['value' => 'cancelled', 'label' => 'Đã hủy'],
            ['value' => 'refunded', 'label' => 'Đã hoàn tiền'],
        ];
    }

    private function getPaymentStatuses()
    {
        return [
            ['value' => '', 'label' => 'Tất cả trạng thái thanh toán'],
            ['value' => 'unpaid', 'label' => 'Chưa thanh toán'],
            ['value' => 'partially_paid', 'label' => 'Thanh toán một phần'],
            ['value' => 'paid', 'label' => 'Đã thanh toán'],
            ['value' => 'refunded', 'label' => 'Đã hoàn tiền'],
        ];
    }

    private function getStatusLabel($status)
    {
        $labels = [
            'pending' => 'Chờ xử lý',
            'confirmed' => 'Đã xác nhận',
            'processing' => 'Đang xử lý',
            'shipping' => 'Đang giao hàng',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy',
            'refunded' => 'Đã hoàn tiền',
        ];

        return $labels[$status] ?? $status;
    }

    private function getPaymentStatusLabel($status)
    {
        $labels = [
            'unpaid' => 'Chưa thanh toán',
            'partially_paid' => 'Thanh toán một phần',
            'paid' => 'Đã thanh toán',
            'refunded' => 'Đã hoàn tiền',
        ];

        return $labels[$status] ?? $status;
    }

    public function destroy(MarketOrder $order)
    {
        try {

            if (!in_array($order->status, ['pending', 'cancelled'])) {
                if (request()->expectsJson()) {
                    return response()->json(['message' => 'Chỉ có thể xóa đơn hàng đang chờ xử lý hoặc đã hủy.'], 400);
                }
                return redirect()->back()->with('flash.error', 'Chỉ có thể xóa đơn hàng đang chờ xử lý hoặc đã hủy.');
            }


            $order->orderDetails()->delete();


            $order->delete();

            if (request()->expectsJson()) {
                return response()->json(['message' => 'Đơn hàng đã được xóa thành công.']);
            }
            return redirect()->back()->with('flash.success', 'Đơn hàng đã được xóa thành công.');
        } catch (ModelNotFoundException $e) {
            if (request()->expectsJson()) {
                return response()->json(['message' => 'Không tìm thấy đơn hàng.'], 404);
            }
            return redirect()->back()->with('flash.error', 'Không tìm thấy đơn hàng.');
        } catch (\Exception $e) {
            if (request()->expectsJson()) {
                return response()->json(['message' => 'Có lỗi xảy ra khi xóa đơn hàng. Vui lòng thử lại.'], 500);
            }
            return redirect()->back()->with('flash.error', 'Có lỗi xảy ra khi xóa đơn hàng. Vui lòng thử lại.');
        }
    }
}
