<?php

namespace App\Services\Marketplace;

use App\Services\SystemSettingService;
use Illuminate\Support\Facades\Config;

class MarketplaceEmailConfigService
{
    private $emailSettings;

    public function __construct()
    {
        $this->emailSettings = SystemSettingService::get('market_email_setting');
    }

    /**
     * Configure mail settings dynamically for marketplace
     */
    public function configureMailSettings()
    {
        if (!$this->emailSettings) {
            return false;
        }


        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp' => [
                'transport' => 'smtp',
                'host' => $this->emailSettings['mail_host'] ?? '',
                'port' => $this->emailSettings['mail_port'] ?? 587,
                'encryption' => $this->emailSettings['mail_encryption'] ?? 'tls',
                'username' => $this->emailSettings['mail_username'] ?? '',
                'password' => $this->emailSettings['mail_password'] ?? '',
                'timeout' => null,
                'local_domain' => env('MAIL_EHLO_DOMAIN'),
            ],
            'mail.from' => [
                'address' => $this->emailSettings['mail_from_address'] ?? '',
                'name' => $this->emailSettings['mail_from_name'] ?? config('app.name'),
            ],
        ]);

        return true;
    }

    /**
     * Check if email settings are configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->emailSettings) &&
               !empty($this->emailSettings['mail_host']) &&
               !empty($this->emailSettings['mail_username']) &&
               !empty($this->emailSettings['mail_from_address']);
    }

    /**
     * Get email settings
     */
    public function getSettings(): ?array
    {
        return $this->emailSettings;
    }

    /**
     * Get from email address
     */
    public function getFromAddress(): string
    {
        return $this->emailSettings['mail_from_address'] ?? config('mail.from.address');
    }

    /**
     * Get from name
     */
    public function getFromName(): string
    {
        return $this->emailSettings['mail_from_name'] ?? config('mail.from.name');
    }
}
