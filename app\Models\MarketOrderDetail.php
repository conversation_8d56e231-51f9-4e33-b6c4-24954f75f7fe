<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class MarketOrderDetail extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'market_order_details';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'market_order_id',
        'order_id',
        'reference_code',
        'product_id',
        'product_name',
        'product_sku',
        'product_image_url',
        'unit_price',
        'quantity',
        'total_price',
        'product_options',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'product_options' => 'json',
    ];

    /**
     * Get the order that owns the order detail.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(MarketOrder::class, 'market_order_id');
    }

    /**
     * Get the product associated with the order detail.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the review associated with this order detail.
     */
    public function review(): HasOne
    {
        return $this->hasOne(MarketProductReview::class, 'market_order_detail_id');
    }

    /**
     * Check if this order detail has been reviewed.
     */
    public function hasReview(): bool
    {
        return $this->review()->exists();
    }
}
