<?php

namespace App\Http\Controllers\Marketplace\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Services\SystemSettingService;

class SettingController extends Controller
{
    public function index()
    {
        return Inertia::render('Marketplace/Settings/Index', [
            'settings' => [
                'general' => [
                    'marketplace_name' => SystemSettingService::get('marketplace_name', 'Pickleball Marketplace'),
                    'marketplace_description' => SystemSettingService::get('marketplace_description', ''),
                    'marketplace_logo' => SystemSettingService::get('marketplace_logo', ''),
                    'maintenance_mode' => SystemSettingService::get('maintenance_mode', false),
                    'allow_guest_checkout' => SystemSettingService::get('allow_guest_checkout', true),
                    'default_currency' => SystemSettingService::get('default_currency', 'VND'),
                    'tax_rate' => SystemSettingService::get('tax_rate', 0),
                ],
                'email' => SystemSettingService::get('market_email_setting', [
                    'email_username' => '',
                    'email_password' => '',
                    'email_host' => 'smtp.gmail.com',
                    'email_send_method' => 'smtp',
                    'email_send_port' => 587,
                    'email_encryption' => 'tls',
                ]),
                'payment' => [
                    'cash_enabled' => SystemSettingService::get('market_cash_payment', ['active' => true])['active'] ?? true,
                    'momo' => SystemSettingService::get('market_momo_payment', [
                        'provider' => 'momo',
                        'partnerCode' => '',
                        'accessKey' => '',
                        'secretKey' => '',
                        'endPoint' => 'https://test-payment.momo.vn/v2/gateway/api/create',
                        'active' => false
                    ]),
                    'vnpay' => SystemSettingService::get('market_vnpay_payment', [
                        'provider' => 'vnpay',
                        'vnp_TmnCode' => '',
                        'vnp_HashSecret' => '',
                        'vnp_Version' => '2.1.1',
                        'endPoint' => 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html',
                        'active' => false
                    ]),
                    'bank_transfer' => SystemSettingService::get('market_bank_transfer', [
                        'accountNumber' => '',
                        'bankName' => '',
                        'accountName' => '',
                        'active' => false
                    ])
                ],
                'shipping' => [
                    'free_shipping_threshold' => SystemSettingService::get('free_shipping_threshold', 0),
                    'default_shipping_fee' => SystemSettingService::get('default_shipping_fee', 30000),
                    'express_shipping_fee' => SystemSettingService::get('express_shipping_fee', 50000),
                    'enable_express_shipping' => SystemSettingService::get('enable_express_shipping', true),
                    'estimated_delivery_days' => SystemSettingService::get('estimated_delivery_days', 3),
                ]
            ]
        ]);
    }

    public function updateGeneral(Request $request)
    {
        $request->validate([
            'marketplace_name' => 'required|string|max:255',
            'marketplace_description' => 'nullable|string',
            'default_currency' => 'required|string|max:3',
            'tax_rate' => 'required|numeric|min:0|max:100',
        ]);

        SystemSettingService::set('marketplace_name', $request->marketplace_name, 'marketplace_general', 'Tên Marketplace');
        SystemSettingService::set('marketplace_description', $request->marketplace_description, 'marketplace_general', 'Mô tả Marketplace');
        SystemSettingService::set('default_currency', $request->default_currency, 'marketplace_general', 'Tiền tệ mặc định');
        SystemSettingService::set('tax_rate', $request->tax_rate, 'marketplace_general', 'Thuế VAT');
        SystemSettingService::set('maintenance_mode', $request->maintenance_mode ?? false, 'marketplace_general', 'Chế độ bảo trì');
        SystemSettingService::set('allow_guest_checkout', $request->allow_guest_checkout ?? false, 'marketplace_general', 'Cho phép mua hàng không đăng ký');

        return back()->with('success', 'Cài đặt chung đã được cập nhật thành công.');
    }

    public function updateEmail(Request $request)
    {
        $request->validate([
            'email_username' => 'required|email',
            'email_host' => 'required|string',
            'email_send_port' => 'required|integer',
        ]);

        $emailSettings = [
            'email_username' => $request->email_username,
            'email_password' => $request->email_password,
            'email_host' => $request->email_host,
            'email_send_method' => $request->email_send_method ?? 'smtp',
            'email_send_port' => $request->email_send_port,
            'email_encryption' => $request->email_encryption ?? 'tls',
        ];

        SystemSettingService::set('market_email_setting', $emailSettings, 'marketplace_email', 'Cài đặt email', 'json');

        return back()->with('success', 'Cài đặt email đã được cập nhật thành công.');
    }

    public function updatePayment(Request $request)
    {
        $request->validate([
            'cash_enabled' => 'boolean',
            'momo.active' => 'boolean',
            'momo.partnerCode' => 'nullable|string',
            'momo.accessKey' => 'nullable|string',
            'momo.secretKey' => 'nullable|string',
            'momo.endPoint' => 'nullable|url',
            'vnpay.active' => 'boolean',
            'vnpay.vnp_TmnCode' => 'nullable|string',
            'vnpay.vnp_HashSecret' => 'nullable|string',
            'vnpay.vnp_Version' => 'nullable|string',
            'vnpay.endPoint' => 'nullable|url',
            'bank_transfer.active' => 'boolean',
            'bank_transfer.accountNumber' => 'nullable|string',
            'bank_transfer.bankName' => 'nullable|string',
            'bank_transfer.accountName' => 'nullable|string',
        ]);


        $cashSettings = ['active' => $request->cash_enabled ?? false];
        SystemSettingService::set('market_cash_payment', $cashSettings, 'marketplace_payment', 'Thanh toán tiền mặt', 'json');


        if ($request->has('momo')) {
            $momoSettings = array_merge([
                'provider' => 'momo',
                'partnerCode' => '',
                'accessKey' => '',
                'secretKey' => '',
                'endPoint' => 'https://test-payment.momo.vn/v2/gateway/api/create',
                'storeId' => '',
                'partnerName' => 'PIBA Marketplace',
                'active' => false
            ], $request->momo);

            SystemSettingService::set('market_momo_payment', $momoSettings, 'marketplace_payment', 'Thanh toán MoMo', 'json');
        }


        if ($request->has('vnpay')) {
            $vnpaySettings = array_merge([
                'provider' => 'vnpay',
                'vnp_TmnCode' => '',
                'vnp_HashSecret' => '',
                'vnp_Version' => '2.1.1',
                'endPoint' => 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html',
                'active' => false
            ], $request->vnpay);

            SystemSettingService::set('market_vnpay_payment', $vnpaySettings, 'marketplace_payment', 'Thanh toán VNPay', 'json');
        }


        if ($request->has('bank_transfer')) {
            $bankSettings = array_merge([
                'accountNumber' => '',
                'bankName' => '',
                'accountName' => '',
                'active' => false
            ], $request->bank_transfer);

            SystemSettingService::set('market_bank_transfer', $bankSettings, 'marketplace_payment', 'Chuyển khoản ngân hàng', 'json');
        }

        return back()->with('success', 'Cài đặt thanh toán đã được cập nhật thành công.');
    }

    public function updateShipping(Request $request)
    {
        $request->validate([
            'free_shipping_threshold' => 'required|numeric|min:0',
            'default_shipping_fee' => 'required|numeric|min:0',
            'express_shipping_fee' => 'required|numeric|min:0',
            'enable_express_shipping' => 'boolean',
            'estimated_delivery_days' => 'required|integer|min:1',
        ]);

        SystemSettingService::set('free_shipping_threshold', $request->free_shipping_threshold, 'marketplace_shipping', 'Ngưỡng miễn phí vận chuyển');
        SystemSettingService::set('default_shipping_fee', $request->default_shipping_fee, 'marketplace_shipping', 'Phí vận chuyển mặc định');
        SystemSettingService::set('express_shipping_fee', $request->express_shipping_fee, 'marketplace_shipping', 'Phí vận chuyển nhanh');
        SystemSettingService::set('enable_express_shipping', $request->enable_express_shipping ?? false, 'marketplace_shipping', 'Cho phép vận chuyển nhanh');
        SystemSettingService::set('estimated_delivery_days', $request->estimated_delivery_days, 'marketplace_shipping', 'Thời gian giao hàng dự kiến');

        return back()->with('success', 'Cài đặt vận chuyển đã được cập nhật thành công.');
    }

    public function getPaymentConfig($provider)
    {
        $configs = [
            'momo' => SystemSettingService::get('market_momo_payment'),
            'vnpay' => SystemSettingService::get('market_vnpay_payment'),
            'bank_transfer' => SystemSettingService::get('market_bank_transfer'),
            'cash' => SystemSettingService::get('market_cash_payment')
        ];

        return response()->json($configs[$provider] ?? null);
    }
}
