<?php

namespace App\Http\Controllers\Marketplace\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class CategoryController extends Controller
{

    public function index(Request $request)
    {
        $query = Category::with('parent', 'children')->withCount('products');


        if ($request->has('search') && $request->search !== null) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description', 'LIKE', "%{$searchTerm}%");
            });
        }


        if ($request->has('status') && $request->status !== null) {
            $status = $request->status === 'true' || $request->status === '1' ? 1 : 0;
            $query->where('status', $status);
        }


        if ($request->has('is_sub') && $request->is_sub !== null) {
            $isSub = $request->is_sub === 'true' || $request->is_sub === '1' ? 1 : 0;
            $query->where('is_sub', $isSub);
        }


        $sortField = $request->sort ?? 'created_at';
        $direction = $request->direction ?? 'desc';
        $query->orderBy($sortField, $direction);

        $categories = $query->paginate(10)->withQueryString();


        $parentCategories = Category::where('is_sub', 0)
            ->where('status', 1)
            ->withCount('products')
            ->get();

        return Inertia::render('Marketplace/Categories/Index', [
            'categories' => $categories,
            'parentCategories' => $parentCategories,
            'filters' => [
                'search' => $request->search,
                'status' => $request->status,
                'is_sub' => $request->is_sub,
                'sort' => $sortField,
                'direction' => $direction,
            ],
        ]);
    }

    public function create()
    {
        $parentCategories = Category::where('is_sub', 0)->where('status', 1)->get();

        return Inertia::render('Marketplace/Categories/Create', [
            'parentCategories' => $parentCategories,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'is_sub' => 'required|boolean',
            'status' => 'required|boolean',
            'image' => 'nullable|image|max:2048',
        ]);

        $data = $request->except('image');
        $data['slug'] = Str::slug($request->name);

        
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('categories', 'public');
            $data['image_url'] = $imagePath;
        }

        $category = Category::create($data);

        return redirect()->route('superadmin.marketplace.categories.index')
            ->with('flash.success', __('marketplace.category_created_successfully'));
    }

    public function show(Category $category)
    {
        $category->load(['parent', 'children.products', 'products']);


        $category->children->each(function ($child) {
            $child->products_count = $child->products->count();
        });

        return Inertia::render('Marketplace/Categories/Show', [
            'category' => $category,
            'products' => $category->products
        ]);
    }

    public function edit(Category $category)
    {
        $category->load('parent', 'children');
        $parentCategories = Category::where('is_sub', 0)
            ->where('status', 1)
            ->where('id', '!=', $category->id)
            ->get();

        return Inertia::render('Marketplace/Categories/Edit', [
            'category' => $category,
            'parentCategories' => $parentCategories,
        ]);
    }

    public function update(Request $request, Category $category)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => [
                'nullable',
                'exists:categories,id',
                function ($attribute, $value, $fail) use ($category) {

                    if ($value === $category->id) {
                        $fail(__('marketplace.category_cannot_be_its_own_parent'));
                    }


                    $childrenIds = $category->children()->pluck('id')->toArray();
                    if (in_array($value, $childrenIds)) {
                        $fail(__('marketplace.category_cannot_have_child_as_parent'));
                    }
                },
            ],
            'is_sub' => 'required|boolean',
            'status' => 'required|boolean',
            'image' => 'nullable|image|max:2048',
        ]);

        $data = $request->except('image');
        $data['slug'] = Str::slug($request->name);


        if ($request->hasFile('image')) {

            if ($category->image_url && Storage::disk('public')->exists($category->image_url)) {
                Storage::disk('public')->delete($category->image_url);
            }

            $imagePath = $request->file('image')->store('categories', 'public');
            $data['image_url'] = $imagePath;
        }

        $category->update($data);

        return redirect()->route('superadmin.marketplace.categories.index')
            ->with('flash.success', __('marketplace.category_updated_successfully'));
    }

    public function destroy(Category $category)
    {

        if ($category->children()->count() > 0) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => __('marketplace.cannot_delete_category_with_subcategories')
                ], 422);
            }

            return redirect()->route('superadmin.marketplace.categories.index')
                ->with('flash.error', __('marketplace.cannot_delete_category_with_subcategories'));
        }


        if ($category->products()->count() > 0) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => __('marketplace.cannot_delete_category_that_has_products')
                ], 422);
            }

            return redirect()->route('superadmin.marketplace.categories.index')
                ->with('flash.error', __('marketplace.cannot_delete_category_that_has_products'));
        }


        if ($category->image_url && Storage::disk('public')->exists($category->image_url)) {
            Storage::disk('public')->delete($category->image_url);
        }

        $category->delete();

        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => __('marketplace.category_deleted_successfully')
            ]);
        }

        return redirect()->route('superadmin.marketplace.categories.index')
            ->with('flash.success', __('marketplace.category_deleted_successfully'));
    }
}
