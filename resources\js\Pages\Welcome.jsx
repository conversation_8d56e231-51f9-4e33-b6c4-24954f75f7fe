import { Head, usePage, router } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import 'react-toastify/dist/ReactToastify.css';

import {
    Header,
    Hero,
    About,
    Cta,
    Footer,
    FeaturesSection,
    ModulesSection,
    TestimonialsSection,
    ContactSection
} from '@/Components/Landing';
import ToastProvider from '@/Components/ToastProvider';
import Loading from '@/Components/Loading';

export default function Welcome() {
    const { flash, errors, siteSettings, landingPageSettings } = usePage().props;
    const [isPageLoading, setIsPageLoading] = useState(false);
    const siteName = siteSettings?.site_name || 'PIBA';

    
    const landingPageType = landingPageSettings?.landing_page_type || 'default';
    const customHtml = landingPageSettings?.landing_custom_html || '';

    
    const showHero = landingPageSettings?.hero_enabled !== 'false';
    const showFeatures = landingPageSettings?.features_enabled !== 'false';
    const showModules = landingPageSettings?.modules_enabled !== 'false';
    const showTestimonials = landingPageSettings?.testimonials_enabled !== 'false';
    const showContact = landingPageSettings?.contact_enabled !== 'false';
    const showCta = landingPageSettings?.cta_enabled !== 'false';

    useEffect(() => {
        const handleStart = () => {
            setIsPageLoading(true);
        };

        const handleFinish = () => {
            setTimeout(() => {
                setIsPageLoading(false);
            }, 150);
        };

        const unsubscribeStart = router.on('start', handleStart);
        const unsubscribeFinish = router.on('finish', handleFinish);
        if (landingPageType === 'custom' && customHtml) {
            setTimeout(() => {
                const customScripts = document.querySelectorAll('.custom-landing-script');
                customScripts.forEach(script => {
                    try {
                        eval(script.textContent);
                    } catch (error) {
                        console.error('Error executing custom script:', error);
                    }
                });
            }, 200);
        }

        return () => {
            unsubscribeStart();
            unsubscribeFinish();
        };
    }, [landingPageType, customHtml]);

    
    if (landingPageType === 'custom' && customHtml) {
        return (
            <>
                <Head title={`Trang chủ - ${siteName}`} />
                {isPageLoading && (
                    <Loading
                        fullScreen
                        text="Đang tải..."
                    />
                )}
                <Header />
                <ToastProvider flash={flash} errors={errors} />
                <div className="custom-landing-content" dangerouslySetInnerHTML={{ __html: customHtml }} />
                <Footer />
            </>
        );
    }

    
    return (
        <>
            <Head title={`Trang chủ - ${siteName}`} />
            {isPageLoading && (
                <Loading
                    fullScreen
                    text="Đang tải..."
                />
            )}
            <Header />
            <ToastProvider flash={flash} errors={errors} />

            {showHero && <Hero
                title={landingPageSettings?.hero_title || 'Welcome to Pickleball'}
                subtitle={landingPageSettings?.hero_subtitle || 'Book your court today'}
                image={landingPageSettings?.hero_image || '/images/default-hero.jpg'}
                buttonText={landingPageSettings?.hero_button_text || 'Book Now'}
                buttonUrl={landingPageSettings?.hero_button_url || '/search'}
            />}

            {showFeatures && <FeaturesSection
                title={landingPageSettings?.features_title || 'Our Features'}
                subtitle={landingPageSettings?.features_subtitle || 'Discover what makes us special'}
            />}

            {showModules && <ModulesSection
                title={landingPageSettings?.modules_title || 'Our Modules'}
                subtitle={landingPageSettings?.modules_subtitle || 'Explore our system modules'}
            />}

            {showTestimonials && <TestimonialsSection
                title={landingPageSettings?.testimonials_title || 'Testimonials'}
                subtitle={landingPageSettings?.testimonials_subtitle || 'What our customers say'}
            />}

            {showContact && <ContactSection
                title={landingPageSettings?.contact_title || 'Contact Us'}
                subtitle={landingPageSettings?.contact_subtitle || 'Get in touch with our team'}
            />}

            {showCta && <Cta
                title={landingPageSettings?.cta_title || 'Ready to get started?'}
                subtitle={landingPageSettings?.cta_subtitle || 'Join us today and experience the difference'}
                buttonText={landingPageSettings?.cta_button_text || 'Sign Up Now'}
                buttonUrl={landingPageSettings?.cta_button_url || '/register'}
            />}

            <Footer />
        </>
    );
}
