import { Head, usePage, router, Link } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import 'react-toastify/dist/ReactToastify.css';

import PrimaryButton from "@/Components/PrimaryButton";
import TextInputWithoutLabel from "@/Components/TextInputWithoutLabel";
import {
    Calendar,
    ShoppingCart,
    GraduationCap,
    Users,
    DollarSign,
    MapPin,
    Clock,
    Star,
    CheckCircle,
    ArrowRight,
    Menu,
} from "lucide-react";
import ToastProvider from '@/Components/ToastProvider';
import Loading from '@/Components/Loading';

export default function Welcome() {
    const { flash, errors, siteSettings, landingPageSettings, auth } = usePage().props;
    const [isPageLoading, setIsPageLoading] = useState(false);
    const siteName = siteSettings?.site_name || 'PickleBall Pro';
    const isLoggedIn = auth && auth.user;

    useEffect(() => {
        const handleStart = () => {
            setIsPageLoading(true);
        };

        const handleFinish = () => {
            setTimeout(() => {
                setIsPageLoading(false);
            }, 150);
        };

        const unsubscribeStart = router.on('start', handleStart);
        const unsubscribeFinish = router.on('finish', handleFinish);

        return () => {
            unsubscribeStart();
            unsubscribeFinish();
        };
    }, []);

    return (
        <>
            <Head title={`Trang chủ - ${siteName}`} />
            {isPageLoading && (
                <Loading
                    fullScreen
                    text="Đang tải..."
                />
            )}
            <ToastProvider flash={flash} errors={errors} />

            <div className="flex flex-col min-h-screen">
                {/* Header */}
                <header className="px-4 lg:px-6 h-16 flex items-center border-b bg-white sticky top-0 z-50">
                    <Link href="/" className="flex items-center justify-center">
                        <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                            <span className="text-white font-bold text-sm">P</span>
                        </div>
                        <span className="ml-2 text-xl font-bold text-green-600">{siteName}</span>
                    </Link>
                    <nav className="ml-auto hidden md:flex gap-6">
                        <Link href="#booking" className="text-sm font-medium hover:text-green-600 transition-colors">
                            Booking
                        </Link>
                        <Link href="#marketplace" className="text-sm font-medium hover:text-green-600 transition-colors">
                            Marketplace
                        </Link>
                        <Link href="#education" className="text-sm font-medium hover:text-green-600 transition-colors">
                            Education
                        </Link>
                        <Link href="#social" className="text-sm font-medium hover:text-green-600 transition-colors">
                            Social
                        </Link>
                        <Link href="#affiliate" className="text-sm font-medium hover:text-green-600 transition-colors">
                            Affiliate
                        </Link>
                    </nav>
                    <PrimaryButton
                        className="ml-4"
                        onClick={() => router.visit(isLoggedIn ? '/search' : '/register')}
                    >
                        {isLoggedIn ? 'Book Court' : 'Get Started'}
                    </PrimaryButton>
                    <button className="md:hidden ml-2 p-2 text-gray-600 hover:text-green-600">
                        <Menu className="h-5 w-5" />
                    </button>
                </header>

                <main className="flex-1">
                    {/* Hero Section */}
                    <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-br from-green-50 to-green-100">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
                                <div className="flex flex-col justify-center space-y-4">
                                    <div className="space-y-2">
                                        <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                                            Your Complete <span className="text-green-600">Pickleball</span> Platform
                                        </h1>
                                        <p className="max-w-[600px] text-gray-600 md:text-xl">
                                            Book courts, buy equipment, learn skills, connect with players, and earn through our comprehensive
                                            pickleball ecosystem.
                                        </p>
                                    </div>
                                    <div className="flex flex-col gap-2 min-[400px]:flex-row">
                                        <PrimaryButton
                                            className="px-6 py-3 text-base"
                                            onClick={() => router.visit(isLoggedIn ? '/search' : '/register')}
                                        >
                                            Start Playing Today
                                            <ArrowRight className="ml-2 h-4 w-4" />
                                        </PrimaryButton>
                                        <button
                                            className="px-6 py-3 text-base bg-white text-green-600 border-2 border-green-600 hover:bg-green-50 rounded-md font-semibold transition duration-150 ease-in-out"
                                        >
                                            Watch Demo
                                        </button>
                                    </div>
                                    <div className="flex items-center gap-4 text-sm text-gray-600">
                                        <div className="flex items-center gap-1">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            <span>Free to join</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            <span>10,000+ active players</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center justify-center">
                                    <img
                                        src="/images/pickleball-hero.jpg"
                                        width="600"
                                        height="400"
                                        alt="Pickleball players on court"
                                        className="mx-auto aspect-video overflow-hidden rounded-xl object-cover shadow-xl"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Booking Module */}
                    <section id="booking" className="w-full py-12 md:py-24 lg:py-32">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                                <div className="space-y-4">
                                    <div className="inline-block rounded-lg bg-green-100 px-3 py-1 text-sm text-green-600">
                                        Court Booking
                                    </div>
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Book Courts Instantly</h2>
                                    <p className="text-gray-600 md:text-lg">
                                        Find and reserve pickleball courts near you with real-time availability. Choose from indoor and
                                        outdoor courts at the best facilities.
                                    </p>
                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="flex items-center gap-2">
                                            <MapPin className="h-5 w-5 text-green-600" />
                                            <span className="text-sm">Find nearby courts</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Clock className="h-5 w-5 text-green-600" />
                                            <span className="text-sm">Real-time availability</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Calendar className="h-5 w-5 text-green-600" />
                                            <span className="text-sm">Easy scheduling</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Star className="h-5 w-5 text-green-600" />
                                            <span className="text-sm">Rated facilities</span>
                                        </div>
                                    </div>
                                    <PrimaryButton
                                        onClick={() => router.visit('/search')}
                                    >
                                        Book a Court Now
                                    </PrimaryButton>
                                </div>
                                <div className="flex justify-center">
                                    <img
                                        src="/images/court-booking.jpg"
                                        width="500"
                                        height="400"
                                        alt="Court booking interface"
                                        className="mx-auto aspect-square overflow-hidden rounded-xl object-cover shadow-lg"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Marketplace Module */}
                    <section id="marketplace" className="w-full py-12 md:py-24 lg:py-32 bg-gray-50">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                                <div className="flex justify-center lg:order-first">
                                    <img
                                        src="/images/marketplace.jpg"
                                        width="500"
                                        height="400"
                                        alt="Pickleball equipment marketplace"
                                        className="mx-auto aspect-square overflow-hidden rounded-xl object-cover shadow-lg"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                                <div className="space-y-4">
                                    <div className="inline-block rounded-lg bg-blue-100 px-3 py-1 text-sm text-blue-600">Marketplace</div>
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Shop Premium Equipment</h2>
                                    <p className="text-gray-600 md:text-lg">
                                        Discover the best pickleball paddles, balls, apparel, and accessories from top brands. Get expert
                                        recommendations and exclusive deals.
                                    </p>
                                    <div className="grid gap-4">
                                        <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
                                            <div className="flex items-center gap-3">
                                                <ShoppingCart className="h-8 w-8 text-blue-600" />
                                                <div>
                                                    <h3 className="font-semibold">Premium Paddles</h3>
                                                    <p className="text-sm text-gray-600">Professional-grade equipment</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
                                            <div className="flex items-center gap-3">
                                                <Star className="h-8 w-8 text-blue-600" />
                                                <div>
                                                    <h3 className="font-semibold">Expert Reviews</h3>
                                                    <p className="text-sm text-gray-600">Detailed product analysis</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-semibold transition duration-150 ease-in-out"
                                        onClick={() => router.visit('/marketplace')}
                                    >
                                        Browse Equipment
                                    </button>
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Education Module */}
                    <section id="education" className="w-full py-12 md:py-24 lg:py-32">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                                <div className="space-y-4">
                                    <div className="inline-block rounded-lg bg-purple-100 px-3 py-1 text-sm text-purple-600">Education</div>
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Master Your Game</h2>
                                    <p className="text-gray-600 md:text-lg">
                                        Learn from certified instructors with comprehensive courses, video tutorials, and personalized
                                        coaching programs for all skill levels.
                                    </p>
                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
                                            <GraduationCap className="h-8 w-8 text-purple-600 mb-2" />
                                            <h3 className="font-semibold mb-1">Video Courses</h3>
                                            <p className="text-sm text-gray-600">Step-by-step tutorials</p>
                                        </div>
                                        <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
                                            <Users className="h-8 w-8 text-purple-600 mb-2" />
                                            <h3 className="font-semibold mb-1">Live Coaching</h3>
                                            <p className="text-sm text-gray-600">One-on-one sessions</p>
                                        </div>
                                    </div>
                                    <button
                                        className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md font-semibold transition duration-150 ease-in-out"
                                        onClick={() => router.visit('/education')}
                                    >
                                        Start Learning
                                    </button>
                                </div>
                                <div className="flex justify-center">
                                    <img
                                        src="/images/education.jpg"
                                        width="500"
                                        height="400"
                                        alt="Pickleball education platform"
                                        className="mx-auto aspect-square overflow-hidden rounded-xl object-cover shadow-lg"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Social Module */}
                    <section id="social" className="w-full py-12 md:py-24 lg:py-32 bg-gray-50">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                                <div className="flex justify-center lg:order-first">
                                    <img
                                        src="/images/social.jpg"
                                        width="500"
                                        height="400"
                                        alt="Pickleball social community"
                                        className="mx-auto aspect-square overflow-hidden rounded-xl object-cover shadow-lg"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                                <div className="space-y-4">
                                    <div className="inline-block rounded-lg bg-orange-100 px-3 py-1 text-sm text-orange-600">
                                        Social Hub
                                    </div>
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Connect & Play Together</h2>
                                    <p className="text-gray-600 md:text-lg">
                                        Join a vibrant community of pickleball enthusiasts. Find playing partners, join tournaments, and share
                                        your passion for the game.
                                    </p>
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-3">
                                            <Users className="h-5 w-5 text-orange-600" />
                                            <span className="text-sm">Find playing partners by skill level</span>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <Calendar className="h-5 w-5 text-orange-600" />
                                            <span className="text-sm">Join local tournaments and events</span>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <Star className="h-5 w-5 text-orange-600" />
                                            <span className="text-sm">Share achievements and progress</span>
                                        </div>
                                    </div>
                                    <button
                                        className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-md font-semibold transition duration-150 ease-in-out"
                                        onClick={() => router.visit('/social')}
                                    >
                                        Join Community
                                    </button>
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Affiliate Module */}
                    <section id="affiliate" className="w-full py-12 md:py-24 lg:py-32">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                                <div className="space-y-4">
                                    <div className="inline-block rounded-lg bg-yellow-100 px-3 py-1 text-sm text-yellow-600">
                                        Affiliate Program
                                    </div>
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Earn While You Play</h2>
                                    <p className="text-gray-600 md:text-lg">
                                        Turn your passion into profit. Refer friends, promote equipment, and earn commissions through our
                                        comprehensive affiliate program.
                                    </p>
                                    <div className="grid gap-4">
                                        <div className="flex items-center gap-3 p-4 bg-yellow-50 rounded-lg">
                                            <DollarSign className="h-8 w-8 text-yellow-600" />
                                            <div>
                                                <h3 className="font-semibold">Up to 15% Commission</h3>
                                                <p className="text-sm text-gray-600">On every successful referral</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-3 p-4 bg-yellow-50 rounded-lg">
                                            <Users className="h-8 w-8 text-yellow-600" />
                                            <div>
                                                <h3 className="font-semibold">Multi-tier Rewards</h3>
                                                <p className="text-sm text-gray-600">Earn from your network's activity</p>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-md font-semibold transition duration-150 ease-in-out"
                                        onClick={() => router.visit('/affiliate')}
                                    >
                                        Become an Affiliate
                                    </button>
                                </div>
                                <div className="flex justify-center">
                                    <img
                                        src="/images/affiliate.jpg"
                                        width="500"
                                        height="400"
                                        alt="Affiliate program dashboard"
                                        className="mx-auto aspect-square overflow-hidden rounded-xl object-cover shadow-lg"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="w-full py-12 md:py-24 lg:py-32 bg-green-600">
                        <div className="container px-4 md:px-6">
                            <div className="flex flex-col items-center justify-center space-y-4 text-center">
                                <div className="space-y-2">
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-white">
                                        Ready to Elevate Your Pickleball Experience?
                                    </h2>
                                    <p className="mx-auto max-w-[700px] text-green-100 md:text-xl">
                                        Join thousands of players who are already booking courts, shopping equipment, learning skills,
                                        connecting with others, and earning through our platform.
                                    </p>
                                </div>
                                <div className="w-full max-w-sm space-y-2">
                                    <form className="flex gap-2" onSubmit={(e) => {
                                        e.preventDefault();
                                        router.visit('/register');
                                    }}>
                                        <Input type="email" placeholder="Enter your email" className="flex-1 bg-white" />
                                        <Button type="submit" variant="secondary" className="bg-white text-green-600 hover:bg-gray-100">
                                            Get Started
                                        </Button>
                                    </form>
                                    <p className="text-xs text-green-100">
                                        Free to join. Start playing today.{" "}
                                        <Link href="/privacy" className="underline underline-offset-2 hover:text-white">
                                            Privacy Policy
                                        </Link>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>

                {/* Footer */}
                <footer className="flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center px-4 md:px-6 border-t bg-gray-50">
                    <p className="text-xs text-gray-600">© 2024 {siteName}. All rights reserved.</p>
                    <nav className="sm:ml-auto flex gap-4 sm:gap-6">
                        <Link href="/terms" className="text-xs hover:underline underline-offset-4 text-gray-600">
                            Terms of Service
                        </Link>
                        <Link href="/privacy" className="text-xs hover:underline underline-offset-4 text-gray-600">
                            Privacy Policy
                        </Link>
                        <Link href="/support" className="text-xs hover:underline underline-offset-4 text-gray-600">
                            Support
                        </Link>
                    </nav>
                </footer>
            </div>
        </>
    );
}
