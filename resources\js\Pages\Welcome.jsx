import { Head, usePage, router, Link } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import 'react-toastify/dist/ReactToastify.css';

import PrimaryButton from "@/Components/PrimaryButton";
import TextInputWithoutLabel from "@/Components/TextInputWithoutLabel";
import {
    Calendar,
    ShoppingCart,
    GraduationCap,
    Users,
    DollarSign,
    MapPin,
    Clock,
    Star,
    CheckCircle,
    ArrowRight,
    Menu,
} from "lucide-react";
import ToastProvider from '@/Components/ToastProvider';
import Loading from '@/Components/Loading';

export default function Welcome() {
    const { flash, errors, siteSettings, landingPageSettings, auth } = usePage().props;
    const [isPageLoading, setIsPageLoading] = useState(false);
    const siteName = siteSettings?.site_name || 'PickleBall Pro';
    const isLoggedIn = auth && auth.user;

    useEffect(() => {
        const handleStart = () => {
            setIsPageLoading(true);
        };

        const handleFinish = () => {
            setTimeout(() => {
                setIsPageLoading(false);
            }, 150);
        };

        const unsubscribeStart = router.on('start', handleStart);
        const unsubscribeFinish = router.on('finish', handleFinish);

        return () => {
            unsubscribeStart();
            unsubscribeFinish();
        };
    }, []);

    return (
        <>
            <Head title={`Trang chủ - ${siteName}`} />
            {isPageLoading && (
                <Loading
                    fullScreen
                    text="Đang tải..."
                />
            )}
            <ToastProvider flash={flash} errors={errors} />

            <div className="flex flex-col min-h-screen">
                {/* Header */}
                <header className="px-4 lg:px-6 h-16 flex items-center border-b bg-white sticky top-0 z-50">
                    <a href="#" className={`logo flex items-center font-bold text-xl text-primary-600`}>
                        <img
                            src={siteSettings?.site_logo || '/images/logo.png'}
                            alt="Logo"
                            className="mr-2 w-13 h-12"
                        />

                        {siteSettings?.show_logo_text === 'on' && (
                            <div
                                className="font-bold text-lg md:text-xl font-montserrat"
                                style={{ color: siteSettings?.logo_text_color || '#16a34a' }}
                            >
                                {siteSettings?.logo_text || siteName}
                            </div>
                        )}
                    </a>
                    <nav className="ml-auto hidden md:flex gap-6">
                        <Link href="#booking" className="text-sm font-medium hover:text-primary-600 transition-colors">
                            Booking
                        </Link>
                        <Link href="#marketplace" className="text-sm font-medium hover:text-primary-600 transition-colors">
                            Marketplace
                        </Link>
                        <Link href="#education" className="text-sm font-medium hover:text-primary-600 transition-colors">
                            Education
                        </Link>
                        <Link href="#social" className="text-sm font-medium hover:text-primary-600 transition-colors">
                            Social
                        </Link>
                        <Link href="#affiliate" className="text-sm font-medium hover:text-primary-600 transition-colors">
                            Affiliate
                        </Link>
                    </nav>
                    <PrimaryButton
                        className="ml-4"
                        onClick={() => router.visit(isLoggedIn ? '/search' : '/register')}
                    >
                        {isLoggedIn ? 'Đặt Sân' : 'Bắt Đầu'}
                    </PrimaryButton>
                    <button className="md:hidden ml-2 p-2 text-gray-600 hover:text-primary-600">
                        <Menu className="h-5 w-5" />
                    </button>
                </header>

                <main className="flex-1">
                    {/* Hero Section */}
                    <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-br from-primary-50 to-primary-100">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
                                <div className="flex flex-col justify-center space-y-4">
                                    <div className="space-y-2">
                                        <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                                            Nền Tảng <span className="text-primary-600">Pickleball</span> Hoàn Chỉnh
                                        </h1>
                                        <p className="max-w-[600px] text-gray-600 md:text-xl">
                                            Đặt sân, mua thiết bị, học kỹ năng, kết nối với người chơi và kiếm tiền thông qua hệ sinh thái
                                            pickleball toàn diện của chúng tôi.
                                        </p>
                                    </div>
                                    <div className="flex flex-col gap-2 min-[400px]:flex-row">
                                        <PrimaryButton
                                            className="px-6 py-3 text-base"
                                            onClick={() => router.visit(isLoggedIn ? '/search' : '/register')}
                                        >
                                            Bắt Đầu Chơi Ngay Hôm Nay
                                            <ArrowRight className="ml-2 h-4 w-4" />
                                        </PrimaryButton>

                                    </div>
                                    <div className="flex items-center gap-4 text-sm text-gray-600">
                                        <div className="flex items-center gap-1">
                                            <CheckCircle className="h-4 w-4 text-primary-600" />
                                            <span>Miễn phí tham gia</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <CheckCircle className="h-4 w-4 text-primary-600" />
                                            <span>Hơn 10,000 người chơi tích cực</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center justify-center">
                                    <img
                                        src="/images/pickleball-hero.jpg"
                                        width="600"
                                        height="400"
                                        alt="Pickleball players on court"
                                        className="mx-auto aspect-video overflow-hidden rounded-xl object-cover shadow-xl"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Booking Module */}
                    <section id="booking" className="w-full py-12 md:py-24 lg:py-32">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                                <div className="space-y-4">
                                    <div className="inline-block rounded-lg bg-primary-100 px-3 py-1 text-sm text-primary-600">
                                        Đặt Sân
                                    </div>
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Đặt Sân Ngay Lập Tức</h2>
                                    <p className="text-gray-600 md:text-lg">
                                        Tìm và đặt trước sân pickleball gần bạn với tình trạng có sẵn theo thời gian thực. Lựa chọn từ các sân
                                        trong nhà và ngoài trời tại các cơ sở tốt nhất.
                                    </p>
                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="flex items-center gap-2">
                                            <MapPin className="h-5 w-5 text-primary-600" />
                                            <span className="text-sm">Tìm sân gần đây</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Clock className="h-5 w-5 text-primary-600" />
                                            <span className="text-sm">Tình trạng theo thời gian thực</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Calendar className="h-5 w-5 text-primary-600" />
                                            <span className="text-sm">Lên lịch dễ dàng</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Star className="h-5 w-5 text-primary-600" />
                                            <span className="text-sm">Cơ sở được đánh giá</span>
                                        </div>
                                    </div>
                                    <PrimaryButton
                                        onClick={() => router.visit('/search')}
                                    >
                                        Đặt Sân Ngay
                                    </PrimaryButton>
                                </div>
                                <div className="flex justify-center">
                                    <img
                                        src="/images/court-booking.jpg"
                                        width="500"
                                        height="400"
                                        alt="Court booking interface"
                                        className="mx-auto aspect-square overflow-hidden rounded-xl object-cover shadow-lg"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Marketplace Module */}
                    <section id="marketplace" className="w-full py-12 md:py-24 lg:py-32 bg-gray-50">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                                <div className="flex justify-center lg:order-first">
                                    <img
                                        src="/images/marketplace.jpg"
                                        width="500"
                                        height="400"
                                        alt="Pickleball equipment marketplace"
                                        className="mx-auto aspect-square overflow-hidden rounded-xl object-cover shadow-lg"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                                <div className="space-y-4">
                                    <div className="inline-block rounded-lg bg-blue-100 px-3 py-1 text-sm text-blue-600">Marketplace</div>
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Mua Sắm Thiết Bị Cao Cấp</h2>
                                    <p className="text-gray-600 md:text-lg">
                                        Khám phá những vợt pickleball, bóng, trang phục và phụ kiện tốt nhất từ các thương hiệu hàng đầu.
                                        Nhận được khuyến nghị từ chuyên gia và ưu đãi độc quyền.
                                    </p>
                                    <div className="grid gap-4">
                                        <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
                                            <div className="flex items-center gap-3">
                                                <ShoppingCart className="h-8 w-8 text-blue-600" />
                                                <div>
                                                    <h3 className="font-semibold">Vợt Cao Cấp</h3>
                                                    <p className="text-sm text-gray-600">Thiết bị chất lượng chuyên nghiệp</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
                                            <div className="flex items-center gap-3">
                                                <Star className="h-8 w-8 text-blue-600" />
                                                <div>
                                                    <h3 className="font-semibold">Đánh Giá Chuyên Gia</h3>
                                                    <p className="text-sm text-gray-600">Phân tích sản phẩm chi tiết</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-semibold transition duration-150 ease-in-out"
                                        onClick={() => router.visit('/marketplace')}
                                    >
                                        Duyệt Thiết Bị
                                    </button>
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Education Module */}
                    <section id="education" className="w-full py-12 md:py-24 lg:py-32">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                                <div className="space-y-4">
                                    <div className="inline-block rounded-lg bg-purple-100 px-3 py-1 text-sm text-purple-600">Education</div>
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Làm Chủ Trò Chơi</h2>
                                    <p className="text-gray-600 md:text-lg">
                                        Học từ các huấn luyện viên được chứng nhận với các khóa học toàn diện, hướng dẫn video và
                                        chương trình huấn luyện cá nhân cho mọi trình độ kỹ năng.
                                    </p>
                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
                                            <GraduationCap className="h-8 w-8 text-purple-600 mb-2" />
                                            <h3 className="font-semibold mb-1">Khóa Học Video</h3>
                                            <p className="text-sm text-gray-600">Hướng dẫn từng bước</p>
                                        </div>
                                        <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
                                            <Users className="h-8 w-8 text-purple-600 mb-2" />
                                            <h3 className="font-semibold mb-1">Huấn Luyện Trực Tiếp</h3>
                                            <p className="text-sm text-gray-600">Buổi học một-đối-một</p>
                                        </div>
                                    </div>
                                    <button
                                        className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md font-semibold transition duration-150 ease-in-out"
                                        onClick={() => router.visit('/education')}
                                    >
                                        Bắt Đầu Học
                                    </button>
                                </div>
                                <div className="flex justify-center">
                                    <img
                                        src="/images/education.jpg"
                                        width="500"
                                        height="400"
                                        alt="Pickleball education platform"
                                        className="mx-auto aspect-square overflow-hidden rounded-xl object-cover shadow-lg"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Social Module */}
                    <section id="social" className="w-full py-12 md:py-24 lg:py-32 bg-gray-50">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                                <div className="flex justify-center lg:order-first">
                                    <img
                                        src="/images/social.jpg"
                                        width="500"
                                        height="400"
                                        alt="Pickleball social community"
                                        className="mx-auto aspect-square overflow-hidden rounded-xl object-cover shadow-lg"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                                <div className="space-y-4">
                                    <div className="inline-block rounded-lg bg-orange-100 px-3 py-1 text-sm text-orange-600">
                                        Trung Tâm Social
                                    </div>
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Kết Nối & Chơi Cùng Nhau</h2>
                                    <p className="text-gray-600 md:text-lg">
                                        Tham gia cộng đồng sôi động của những người đam mê pickleball. Tìm đối tác chơi, tham gia giải đấu
                                        và chia sẻ niềm đam mê trò chơi của bạn.
                                    </p>
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-3">
                                            <Users className="h-5 w-5 text-orange-600" />
                                            <span className="text-sm">Tìm đối tác chơi theo trình độ kỹ năng</span>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <Calendar className="h-5 w-5 text-orange-600" />
                                            <span className="text-sm">Tham gia giải đấu và sự kiện địa phương</span>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <Star className="h-5 w-5 text-orange-600" />
                                            <span className="text-sm">Chia sẻ thành tích và tiến bộ</span>
                                        </div>
                                    </div>
                                    <button
                                        className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-md font-semibold transition duration-150 ease-in-out"
                                        onClick={() => router.visit('/social')}
                                    >
                                        Tham Gia Cộng Đồng
                                    </button>
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Affiliate Module */}
                    <section id="affiliate" className="w-full py-12 md:py-24 lg:py-32">
                        <div className="container px-4 md:px-6">
                            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                                <div className="space-y-4">
                                    <div className="inline-block rounded-lg bg-yellow-100 px-3 py-1 text-sm text-yellow-600">
                                        Chương Trình Affiliate
                                    </div>
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Kiếm Tiền Khi Chơi</h2>
                                    <p className="text-gray-600 md:text-lg">
                                        Biến niềm đam mê thành lợi nhuận. Giới thiệu bạn bè, quảng bá thiết bị và kiếm hoa hồng thông qua
                                        chương trình affiliate toàn diện của chúng tôi.
                                    </p>
                                    <div className="grid gap-4">
                                        <div className="flex items-center gap-3 p-4 bg-yellow-50 rounded-lg">
                                            <DollarSign className="h-8 w-8 text-yellow-600" />
                                            <div>
                                                <h3 className="font-semibold">Hoa Hồng Lên Đến 15%</h3>
                                                <p className="text-sm text-gray-600">Cho mỗi lần giới thiệu thành công</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-3 p-4 bg-yellow-50 rounded-lg">
                                            <Users className="h-8 w-8 text-yellow-600" />
                                            <div>
                                                <h3 className="font-semibold">Phần Thưởng Đa Cấp</h3>
                                                <p className="text-sm text-gray-600">Kiếm tiền từ hoạt động mạng lưới của bạn</p>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-md font-semibold transition duration-150 ease-in-out"
                                        onClick={() => router.visit('/affiliate')}
                                    >
                                        Trở Thành Affiliate
                                    </button>
                                </div>
                                <div className="flex justify-center">
                                    <img
                                        src="/images/affiliate.jpg"
                                        width="500"
                                        height="400"
                                        alt="Affiliate program dashboard"
                                        className="mx-auto aspect-square overflow-hidden rounded-xl object-cover shadow-lg"
                                        onError={(e) => {
                                            e.target.src = '/images/default-hero.jpg';
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="w-full py-12 md:py-24 lg:py-32 bg-primary-600">
                        <div className="container px-4 md:px-6">
                            <div className="flex flex-col items-center justify-center space-y-4 text-center">
                                <div className="space-y-2">
                                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-white">
                                        Sẵn Sàng Nâng Cao Trải Nghiệm Pickleball?
                                    </h2>
                                    <p className="mx-auto max-w-[700px] text-primary-100 md:text-xl">
                                        Tham gia cùng hàng nghìn người chơi đã đặt sân, mua sắm thiết bị, học kỹ năng,
                                        kết nối với người khác và kiếm tiền thông qua nền tảng của chúng tôi.
                                    </p>
                                </div>
                                <div className="w-full max-w-sm space-y-2">
                                    <form className="flex gap-2" onSubmit={(e) => {
                                        e.preventDefault();
                                        router.visit('/register');
                                    }}>
                                        <TextInputWithoutLabel type="email" placeholder="Nhập email của bạn" className="flex-1 bg-white border-white" />
                                        <button type="submit" className="px-4 py-2 bg-white text-primary-600 hover:bg-gray-100 rounded-md font-semibold transition duration-150 ease-in-out">
                                            Bắt Đầu
                                        </button>
                                    </form>
                                    <p className="text-xs text-primary-100">
                                        Miễn phí tham gia. Bắt đầu chơi ngay hôm nay.{" "}
                                        <Link href="/privacy" className="underline underline-offset-2 hover:text-white">
                                            Chính Sách Bảo Mật
                                        </Link>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>

                {/* Footer */}
                <footer className="flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center px-4 md:px-6 border-t bg-gray-50">
                    <p className="text-xs text-gray-600">© 2024 {siteName}. Tất cả quyền được bảo lưu.</p>
                    <nav className="sm:ml-auto flex gap-4 sm:gap-6">
                        <Link href="/terms" className="text-xs hover:underline underline-offset-4 text-gray-600">
                            Điều Khoản Dịch Vụ
                        </Link>
                        <Link href="/privacy" className="text-xs hover:underline underline-offset-4 text-gray-600">
                            Chính Sách Bảo Mật
                        </Link>
                        <Link href="/support" className="text-xs hover:underline underline-offset-4 text-gray-600">
                            Hỗ Trợ
                        </Link>
                    </nav>
                </footer>
            </div>
        </>
    );
}
