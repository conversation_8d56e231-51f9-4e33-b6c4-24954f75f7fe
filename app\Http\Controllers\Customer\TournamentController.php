<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Tournament;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TournamentController extends Controller
{
    /**
     * Display a listing of tournaments for customers.
     */
    public function index(Request $request)
    {
        $query = Tournament::with('business');

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->byStatus($request->status);
        }

        // Filter by tournament type
        if ($request->filled('type')) {
            if ($request->type === 'system') {
                $query->systemWide();
            } elseif ($request->type === 'business') {
                $query->businessTournaments();
            }
        }

        // Filter featured tournaments
        if ($request->boolean('featured')) {
            $query->featured();
        }

        // Sort by featured first, then by start date
        $query->orderBy('featured', 'desc')
              ->orderBy('start_date', 'asc');

        $tournaments = $query->paginate(12)->withQueryString();

        return Inertia::render('Customers/Tournament', [
            'tournaments' => $tournaments,
            'filters' => $request->only(['search', 'status', 'type', 'featured']),
            'statuses' => Tournament::getStatuses(),
        ]);
    }

    /**
     * Get tournaments data via API.
     */
    public function apiIndex(Request $request)
    {
        $query = Tournament::with('business');

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->byStatus($request->status);
        }

        // Filter by tournament type
        if ($request->filled('type')) {
            if ($request->type === 'system') {
                $query->systemWide();
            } elseif ($request->type === 'business') {
                $query->businessTournaments();
            }
        }

        // Filter featured tournaments
        if ($request->boolean('featured')) {
            $query->featured();
        }

        // Sort by featured first, then by start date
        $query->orderBy('featured', 'desc')
              ->orderBy('start_date', 'asc');

        $tournaments = $query->paginate(12)->withQueryString();

        return response()->json([
            'success' => true,
            'data' => $tournaments,
            'statuses' => Tournament::getStatuses(),
        ]);
    }

    /**
     * Display the specified tournament.
     */
    public function show(Tournament $tournament)
    {
        $tournament->load('business');

        return Inertia::render('Customers/TournamentDetail', [
            'tournament' => $tournament,
        ]);
    }
}
