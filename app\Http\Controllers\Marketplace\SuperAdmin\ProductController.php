<?php

namespace App\Http\Controllers\Marketplace\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class ProductController extends Controller
{

    protected function applyFilters($query, Request $request)
    {

        if ($request->has('search') && $request->search !== null && $request->search !== '' && $request->search !== 'null' && $request->search !== 'undefined') {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('sku', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('barcode', 'LIKE', "%{$searchTerm}%");
            });
        }


        if (
            $request->has('category_id') &&
            $request->category_id !== null &&
            $request->category_id !== '' &&
            $request->category_id !== 'null' &&
            $request->category_id !== 'undefined'
        ) {
            $categoryId = $request->category_id;


            $categoryIds = [$categoryId];
            $category = \App\Models\Category::find($categoryId);
            if ($category) {

                $childIds = $category->children()->pluck('id')->toArray();

                $categoryIds = array_merge($categoryIds, $childIds);
            }

            $query->whereIn('category_id', $categoryIds);
        }


        if ($request->has('status') && $request->status !== null && $request->status !== '' && $request->status !== 'null' && $request->status !== 'undefined') {
            $status = ($request->status === 'true' || $request->status === '1' || $request->status === 1) ? 1 : 0;
            $query->where('status', $status);
        }


        if ($request->has('is_featured') && $request->is_featured !== null && $request->is_featured !== '' && $request->is_featured !== 'null' && $request->is_featured !== 'undefined') {
            $isFeatured = ($request->is_featured === 'true' || $request->is_featured === '1' || $request->is_featured === 1) ? 1 : 0;
            $query->where('is_featured', $isFeatured);
        }

        $sortField = $request->sort ?? 'created_at';
        $direction = $request->direction ?? 'desc';
        $query->orderBy($sortField, $direction);

        return $query;
    }

    public function index(Request $request)
    {
        $query = Product::with('category');
        $this->applyFilters($query, $request);

        $products = $query->paginate(10)->withQueryString();

        $categories = Category::where('status', 1)
            ->withCount('products')
            ->get();

        return Inertia::render('Marketplace/Products/Index', [
            'products' => $products,
            'categories' => $categories,
            'filters' => [
                'search' => $request->search,
                'category_id' => $request->category_id,
                'status' => $request->status,
                'is_featured' => $request->is_featured,
                'sort' => $request->sort ?? 'created_at',
                'direction' => $request->direction ?? 'desc',
            ],
        ]);
    }


    public function apiIndex(Request $request)
    {
        $query = Product::with('category');
        $this->applyFilters($query, $request);

        $products = $query->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }


    public function create()
    {
        $categories = Category::where('status', 1)->get();

        return Inertia::render('Marketplace/Products/Create', [
            'categories' => $categories,
        ]);
    }


    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'sku' => 'nullable|string|max:100|unique:products,sku',
            'barcode' => 'nullable|string|max:100',
            'unit' => 'nullable|string|max:50',
            'import_price' => 'required|numeric|min:0',
            'sale_price' => 'required|numeric|min:0',
            'alert_quantity' => 'nullable|integer|min:0',
            'quantity' => 'required|integer|min:0',
            'brand' => 'nullable|string|max:100',
            'image' => 'nullable|image|max:2048',
            'status' => 'required|boolean',
            'is_featured' => 'nullable|boolean',
            'weight' => 'nullable|string|max:100',
            'dimensions' => 'nullable|string|max:100',
        ]);

        $data = $request->except('image');


        if ($request->name) {
            $data['slug'] = Str::slug($request->name);
        }


        if ($request->hasFile('image')) {
            $data['image_url'] = $request->file('image')->store('products', 'public');
        }

        $product = Product::create($data);

        return redirect()->route('superadmin.marketplace.products.index')
            ->with('flash.success', __('marketplace.product_created_successfully'));
    }


    public function show(Product $product)
    {
        $product->load('category');

        return Inertia::render('Marketplace/Products/Show', [
            'product' => $product
        ]);
    }


    public function edit(Product $product)
    {
        $product->load('category');
        $categories = Category::where('status', 1)->get();

        return Inertia::render('Marketplace/Products/Edit', [
            'product' => $product,
            'categories' => $categories,
        ]);
    }


    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'sku' => 'nullable|string|max:100|unique:products,sku,' . $product->id,
            'barcode' => 'nullable|string|max:100',
            'unit' => 'nullable|string|max:50',
            'import_price' => 'required|numeric|min:0',
            'sale_price' => 'required|numeric|min:0',
            'alert_quantity' => 'nullable|integer|min:0',
            'quantity' => 'required|integer|min:0',
            'brand' => 'nullable|string|max:100',
            'image' => 'nullable|image|max:2048',
            'status' => 'required|boolean',
            'is_featured' => 'nullable|boolean',
            'weight' => 'nullable|string|max:100',
            'dimensions' => 'nullable|string|max:100',
        ]);

        $data = $request->except('image');


        if ($request->name) {
            $data['slug'] = Str::slug($request->name);
        }


        if ($request->hasFile('image')) {

            if ($product->image_url && Storage::disk('public')->exists($product->image_url)) {
                Storage::disk('public')->delete($product->image_url);
            }

            $data['image_url'] = $request->file('image')->store('products', 'public');
        }

        $product->update($data);

        return redirect()->route('superadmin.marketplace.products.index')
            ->with('flash.success', __('marketplace.product_updated_successfully'));
    }


    public function destroy(Product $product)
    {

        if ($product->image_url && Storage::disk('public')->exists($product->image_url)) {
            Storage::disk('public')->delete($product->image_url);
        }

        $product->delete();

        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => __('marketplace.product_deleted_successfully')
            ]);
        }

        return redirect()->route('superadmin.marketplace.products.index')
            ->with('flash.success', __('marketplace.product_deleted_successfully'));
    }
}
