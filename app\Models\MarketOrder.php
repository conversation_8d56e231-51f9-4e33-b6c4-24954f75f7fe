<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MarketOrder extends Model
{
    use HasFactory;

    protected $table = 'market_orders';

    protected $fillable = [
        'order_number',
        'user_id',
        'customer_id',
        'customer_name',
        'customer_phone',
        'customer_email',
        'shipping_address',
        'province_name',
        'district_name',
        'ward_name',
        'province_id',
        'district_id',
        'ward_id',
        'payment_method',
        'subtotal',
        'shipping_fee',
        'discount_amount',
        'total_amount',
        'status',
        'payment_status',
        'notes',
        'tracking_number',
        'shipped_at',
        'delivered_at',
        'metadata',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'shipping_fee' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'metadata' => 'json',
    ];

    protected $dates = [
        'shipped_at',
        'delivered_at',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function orderDetails(): HasMany
    {
        return $this->hasMany(MarketOrderDetail::class, 'market_order_id');
    }

    public function payments(): HasMany
    {
        return $this->hasMany(MarketPayment::class, 'market_order_id');
    }

    public function province(): BelongsTo
    {
        return $this->belongsTo(Province::class, 'province_id');
    }

    public function district(): BelongsTo
    {
        return $this->belongsTo(District::class, 'district_id');
    }

    public function ward(): BelongsTo
    {
        return $this->belongsTo(Ward::class, 'ward_id');
    }

    public function getPaymentMethodConfigAttribute()
    {
        if (!$this->payment_method) {
            return null;
        }

        $settingKey = "market_{$this->payment_method}_payment";
        return app(\App\Services\SystemSettingService::class)::get($settingKey, []);
    }

    public function getPaymentMethodNameAttribute()
    {
        $methodNames = [
            'cash' => 'Thanh toán khi nhận hàng (COD)',
            'momo' => 'Ví điện tử MoMo',
            'vnpay' => 'VNPay',
            'bank_transfer' => 'Chuyển khoản ngân hàng'
        ];

        return $methodNames[$this->payment_method] ?? $this->payment_method;
    }

    public static function generateOrderNumber(): string
    {
        $prefix = 'MKT';
        $timestamp = now()->format('ymd');

        do {
            $randomNumber = mt_rand(1000, 9999);
            $orderNumber = $prefix . $timestamp . $randomNumber;
            $exists = self::where('order_number', $orderNumber)->exists();
        } while ($exists);

        return $orderNumber;
    }

    public function generateTransactionId(string $paymentMethod = null): string
    {
        $prefix = '';
        switch ($paymentMethod ?? $this->payment_method) {
            case 'cash':
                $prefix = 'CASH';
                break;
            case 'bank_transfer':
                $prefix = 'BANK';
                break;
            case 'momo':
                $prefix = 'MOMO';
                break;
            case 'vnpay':
                $prefix = 'VNP';
                break;
            default:
                $prefix = 'TXN';
                break;
        }

        $timestamp = now()->format('ymdHis');
        $orderNumber = preg_replace('/[^0-9]/', '', $this->order_number);

        return $prefix . $timestamp . $orderNumber;
    }

    public function calculateTotal(): void
    {
        $subtotal = $this->orderDetails->sum('total_price');
        $this->subtotal = $subtotal;


        $this->total_amount = $this->subtotal + $this->shipping_fee - $this->discount_amount;

        $this->save();
    }

    public function couponUsages(): HasMany
    {
        return $this->hasMany(MarketCouponUsage::class, 'order_id');
    }

    /**
     * Get all reviews for this order.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(MarketProductReview::class, 'market_order_id');
    }

    /**
     * Check if order can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return $this->status === 'pending' && $this->payment_status !== 'paid';
    }

    /**
     * Cancel the order.
     */
    public function cancel(string $reason = null): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        $this->status = 'cancelled';
        $this->notes = $this->notes ? $this->notes . "\n" . "Cancelled: " . ($reason ?: 'Customer request') : "Cancelled: " . ($reason ?: 'Customer request');

        $this->payments()->where('status', 'pending')->update([
            'status' => 'cancelled',
            'notes' => 'Order cancelled'
        ]);

        return $this->save();
    }

    /**
     * Check if order is completed and can be reviewed.
     */
    public function canBeReviewed(): bool
    {
        return $this->status === 'completed' || $this->status === 'delivered';
    }

    /**
     * Get order details that can be reviewed (not yet reviewed).
     */
    public function getReviewableItems()
    {
        if (!$this->canBeReviewed()) {
            return collect();
        }

        return $this->orderDetails()->whereDoesntHave('review')->get();
    }

    /**
     * Check if all items in this order have been reviewed.
     */
    public function isFullyReviewed(): bool
    {
        if (!$this->canBeReviewed()) {
            return false;
        }

        return $this->orderDetails()->count() === $this->reviews()->count();
    }
}
