<?php

namespace App\Http\Controllers\Marketplace\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\MarketOrder;
use App\Models\MarketPayment;
use App\Services\SystemSettingService;
use App\Services\Marketplace\MarketplaceMailService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class VNPayController extends Controller
{
    private function getVNPayConfig()
    {
        $settings = SystemSettingService::get('market_vnpay_payment', []);

        return [
            'vnp_TmnCode' => $settings['vnp_TmnCode'] ?? '',
            'vnp_HashSecret' => $settings['vnp_HashSecret'] ?? '',
            'vnp_Url' => $settings['endPoint'] ?? 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html',
            'vnp_Version' => $settings['vnp_Version'] ?? '2.1.0',
        ];
    }

    public function createPayment(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:market_orders,id',
            'return_url' => 'nullable|url'
        ]);

        $order = MarketOrder::findOrFail($request->order_id);


        if ($order instanceof \Illuminate\Database\Eloquent\Collection) {
            $order = $order->first();
        }

        if (!Auth::check() || $order->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Không có quyền truy cập đơn hàng này'
            ], 403);
        }


        if ($order->payment_status !== 'unpaid') {
            return response()->json([
                'success' => false,
                'message' => 'Đơn hàng đã được thanh toán hoặc không thể thanh toán'
            ], 400);
        }


        if ($order->payment_url_created_at && $order->vnpay_payment_url) {
            $createdAt = Carbon::parse($order->payment_url_created_at);
            if ($createdAt->diffInMinutes(now()) < 30) {

                return response()->json([
                    'success' => true,
                    'payment_url' => $order->vnpay_payment_url,
                    'message' => 'Sử dụng liên kết thanh toán VNPay đã có',
                    'is_existing_url' => true
                ]);
            }
        }

        $config = $this->getVNPayConfig();

        if (empty($config['vnp_TmnCode']) || empty($config['vnp_HashSecret'])) {
            return response()->json([
                'success' => false,
                'message' => 'Cấu hình VNPay chưa được thiết lập'
            ], 500);
        }


        $vnpayUrl = $this->buildVNPayUrl($order, $config, $request->return_url);

        return response()->json([
            'success' => true,
            'payment_url' => $vnpayUrl,
            'message' => 'Chuyển hướng đến VNPay để thanh toán'
        ]);
    }

    private function buildVNPayUrl(MarketOrder $order, array $config, $returnUrl = null)
    {

        $timestamp = time();
        $microtime = round(microtime(true) * 1000);
        $randomStr = substr(str_shuffle('abcdefghijklmnopqrstuvwxyz0123456789'), 0, 8);
        $vnp_TxnRef = $order->order_number . '_' . $timestamp . '_' . $randomStr;

        $vnp_OrderInfo = "Thanh toan don hang {$order->order_number}";
        $vnp_OrderType = 'billpayment';
        $vnp_Amount = $order->total_amount * 100;
        $vnp_Locale = 'vn';
        $vnp_IpAddr = request()->ip();

        $vnp_ReturnUrl = route('marketplace.payment.vnpay.return');
        $vnp_CreateDate = Carbon::now()->format('YmdHis');
        $vnp_ExpireDate = Carbon::now()->addMinutes(15)->format('YmdHis');

        $inputData = [
            "vnp_Version" => $config['vnp_Version'],
            "vnp_TmnCode" => $config['vnp_TmnCode'],
            "vnp_Amount" => $vnp_Amount,
            "vnp_Command" => "pay",
            "vnp_CreateDate" => $vnp_CreateDate,
            "vnp_CurrCode" => "VND",
            "vnp_IpAddr" => $vnp_IpAddr,
            "vnp_Locale" => $vnp_Locale,
            "vnp_OrderInfo" => $vnp_OrderInfo,
            "vnp_OrderType" => $vnp_OrderType,
            "vnp_ReturnUrl" => $vnp_ReturnUrl,
            "vnp_TxnRef" => $vnp_TxnRef,
            "vnp_ExpireDate" => $vnp_ExpireDate
        ];

        ksort($inputData);
        $query = "";
        $i = 0;
        $hashdata = "";

        foreach ($inputData as $key => $value) {
            if ($i == 1) {
                $hashdata .= '&' . urlencode($key) . "=" . urlencode($value);
            } else {
                $hashdata .= urlencode($key) . "=" . urlencode($value);
                $i = 1;
            }
            $query .= urlencode($key) . "=" . urlencode($value) . '&';
        }

        $vnp_Url = $config['vnp_Url'] . "?" . $query;
        $vnpSecureHash = hash_hmac('sha512', $hashdata, $config['vnp_HashSecret']);
        $vnp_Url .= 'vnp_SecureHash=' . $vnpSecureHash;


        $order->vnpay_payment_url = $vnp_Url;
        $order->vnpay_order_id = $vnp_TxnRef;
        $order->payment_url_created_at = now();
        $order->save();

        Log::info('VNPay payment URL created', [
            'order_id' => $order->id,
            'vnp_TxnRef' => $vnp_TxnRef,
            'payment_url' => $vnp_Url
        ]);

        return $vnp_Url;
    }

    public function handleReturn(Request $request)
    {
        $config = $this->getVNPayConfig();
        $inputData = [];

        foreach ($request->all() as $key => $value) {
            if (substr($key, 0, 4) == "vnp_") {
                $inputData[$key] = $value;
            }
        }


        if (!isset($inputData['vnp_SecureHash'])) {
            return redirect()->route('marketplace.checkout.failure', [
                'message' => 'Thiếu thông tin xác thực từ VNPay'
            ]);
        }

        $vnp_SecureHash = $inputData['vnp_SecureHash'];
        unset($inputData['vnp_SecureHash']);
        ksort($inputData);

        $hashdata = "";
        $i = 0;
        foreach ($inputData as $key => $value) {
            if ($i == 1) {
                $hashdata .= '&' . urlencode($key) . "=" . urlencode($value);
            } else {
                $hashdata .= urlencode($key) . "=" . urlencode($value);
                $i = 1;
            }
        }

        $secureHash = hash_hmac('sha512', $hashdata, $config['vnp_HashSecret']);


        Log::info('VNPay Return Data', [
            'input_data' => $inputData,
            'calculated_hash' => $secureHash,
            'received_hash' => $vnp_SecureHash,
            'hash_valid' => $secureHash == $vnp_SecureHash
        ]);

        if ($secureHash == $vnp_SecureHash) {
            $vnpTxnRef = $request->vnp_TxnRef;
            $responseCode = $request->vnp_ResponseCode;
            $transactionStatus = $request->vnp_TransactionStatus;
            $transactionNo = $request->vnp_TransactionNo;


            $order = MarketOrder::where('vnpay_order_id', $vnpTxnRef)->first();


            if (!$order) {


                $orderNumberParts = explode('_', $vnpTxnRef);
                if (count($orderNumberParts) > 1) {
                    $originalOrderNumber = $orderNumberParts[0];
                    $order = MarketOrder::where('order_number', $originalOrderNumber)->first();
                } else {

                    $order = MarketOrder::where('order_number', $vnpTxnRef)->first();
                }

                Log::info('VNPay Return Order Search', [
                    'vnpay_txn_ref' => $vnpTxnRef,
                    'found_by_fallback' => $order ? true : false
                ]);
            }

            if ($order) {

                if ($responseCode == '00' && $transactionStatus == '00') {

                    $order->update([
                        'payment_status' => 'paid',
                        'status' => 'confirmed'
                    ]);


                    $payment = MarketPayment::where('market_order_id', $order->id)
                        ->where('payment_method', 'vnpay')
                        ->first();

                    if ($payment) {
                        $payment->update([
                            'status' => 'completed',
                            'transaction_id' => $transactionNo,
                            'payment_details' => $inputData,
                            'transaction_date' => now(),
                            'reference_number' => $vnpTxnRef,
                            'approved_by' => 'VNPay',
                            'approved_at' => now()
                        ]);
                    }

                    Log::info('VNPay payment successful via return URL', [
                        'order_number' => $order->order_number,
                        'vnp_txn_ref' => $vnpTxnRef,
                        'transaction_id' => $transactionNo
                    ]);


                    try {
                        MarketplaceMailService::sendPaymentConfirmation($order, $payment);
                    } catch (\Exception $e) {
                        Log::warning('Failed to send payment confirmation email', [
                            'order_id' => $order->id,
                            'payment_id' => $payment ? $payment->id : null,
                            'error' => $e->getMessage()
                        ]);
                    }

                    return redirect()->route('marketplace.checkout.success', ['order_id' => $order->id]);
                } else {

                    $payment = MarketPayment::where('market_order_id', $order->id)
                        ->where('payment_method', 'vnpay')
                        ->first();

                    if ($payment) {
                        $payment->update([
                            'status' => 'failed',
                            'payment_details' => $inputData,
                            'transaction_date' => now(),
                            'reference_number' => $vnpTxnRef
                        ]);
                    }

                    Log::warning('VNPay payment failed via return URL', [
                        'order_number' => $order->order_number,
                        'vnp_txn_ref' => $vnpTxnRef,
                        'response_code' => $responseCode,
                        'transaction_status' => $transactionStatus
                    ]);


                    $message = $this->getVNPayErrorMessage($responseCode);
                    return redirect()->route('marketplace.checkout.failure', [
                        'order_id' => $order->id,
                        'message' => $message
                    ]);
                }
            } else {
                return redirect()->route('marketplace.checkout.failure', [
                    'message' => 'Không tìm thấy đơn hàng với mã tham chiếu: ' . $vnpTxnRef
                ]);
            }
        } else {
            return redirect()->route('marketplace.checkout.failure', [
                'message' => 'Chữ ký không hợp lệ từ VNPay'
            ]);
        }
    }

    public function handleIPN(Request $request)
    {
        $config = $this->getVNPayConfig();
        $inputData = [];
        $returnData = [];

        foreach ($request->all() as $key => $value) {
            if (substr($key, 0, 4) == "vnp_") {
                $inputData[$key] = $value;
            }
        }

        $vnp_SecureHash = $inputData['vnp_SecureHash'];
        unset($inputData['vnp_SecureHash']);
        ksort($inputData);

        $hashdata = "";
        $i = 0;
        foreach ($inputData as $key => $value) {
            if ($i == 1) {
                $hashdata .= '&' . urlencode($key) . "=" . urlencode($value);
            } else {
                $hashdata .= urlencode($key) . "=" . urlencode($value);
                $i = 1;
            }
        }

        $secureHash = hash_hmac('sha512', $hashdata, $config['vnp_HashSecret']);

        try {
            if ($secureHash == $vnp_SecureHash) {
                $vnpTxnRef = $inputData['vnp_TxnRef'];
                $vnp_Amount = $inputData['vnp_Amount'] / 100;
                $vnp_ResponseCode = $inputData['vnp_ResponseCode'];
                $vnp_TransactionNo = $inputData['vnp_TransactionNo'];
                $vnp_BankCode = $inputData['vnp_BankCode'];
                $vnp_PayDate = $inputData['vnp_PayDate'] ?? null;


                $order = MarketOrder::where('vnpay_order_id', $vnpTxnRef)->first();


                if (!$order) {


                    $orderNumberParts = explode('_', $vnpTxnRef);
                    if (count($orderNumberParts) > 1) {
                        $originalOrderNumber = $orderNumberParts[0];
                        $order = MarketOrder::where('order_number', $originalOrderNumber)->first();
                    } else {

                        $order = MarketOrder::where('order_number', $vnpTxnRef)->first();
                    }

                    Log::info('VNPay IPN Order Search', [
                        'vnpay_txn_ref' => $vnpTxnRef,
                        'found_by_fallback' => $order ? true : false
                    ]);
                }

                if ($order) {
                    if ($order->total_amount == $vnp_Amount) {
                        if ($order->payment_status == 'unpaid') {
                            if ($vnp_ResponseCode == '00') {

                                $order->update([
                                    'payment_status' => 'paid',
                                    'status' => 'confirmed'
                                ]);


                                $payment = MarketPayment::where('market_order_id', $order->id)
                                    ->where('payment_method', 'vnpay')
                                    ->first();

                                if ($payment) {
                                    $payment->update([
                                        'status' => 'completed',
                                        'transaction_id' => $vnp_TransactionNo,
                                        'payment_details' => $inputData,
                                        'reference_number' => $vnpTxnRef,
                                        'transaction_date' => now(),
                                        'approved_by' => 'VNPay',
                                        'approved_at' => now()
                                    ]);
                                }

                                Log::info('VNPay payment successful', [
                                    'order_number' => $order->order_number,
                                    'vnp_txn_ref' => $vnpTxnRef,
                                    'transaction_id' => $vnp_TransactionNo,
                                    'amount' => $vnp_Amount
                                ]);


                                try {
                                    MarketplaceMailService::sendPaymentConfirmation($order, $payment);
                                } catch (\Exception $e) {
                                    Log::warning('Failed to send payment confirmation email', [
                                        'order_id' => $order->id,
                                        'payment_id' => $payment ? $payment->id : null,
                                        'error' => $e->getMessage()
                                    ]);
                                }

                                $returnData['RspCode'] = '00';
                                $returnData['Message'] = 'Confirm Success';
                            } else {

                                $payment = MarketPayment::where('market_order_id', $order->id)
                                    ->where('payment_method', 'vnpay')
                                    ->first();

                                if ($payment) {
                                    $payment->update([
                                        'status' => 'failed',
                                        'payment_details' => $inputData,
                                        'reference_number' => $vnpTxnRef,
                                        'transaction_date' => now()
                                    ]);
                                }

                                Log::warning('VNPay payment failed', [
                                    'order_number' => $order->order_number,
                                    'vnp_txn_ref' => $vnpTxnRef,
                                    'response_code' => $vnp_ResponseCode,
                                    'amount' => $vnp_Amount
                                ]);

                                $returnData['RspCode'] = '00';
                                $returnData['Message'] = 'Payment failed but confirmed';
                            }
                        } else {
                            $returnData['RspCode'] = '02';
                            $returnData['Message'] = 'Order already confirmed';
                        }
                    } else {
                        $returnData['RspCode'] = '04';
                        $returnData['Message'] = 'Invalid amount';
                    }
                } else {
                    $returnData['RspCode'] = '01';
                    $returnData['Message'] = 'Order not found';
                }
            } else {
                $returnData['RspCode'] = '97';
                $returnData['Message'] = 'Invalid signature';
            }
        } catch (\Exception $e) {
            Log::error('VNPay IPN error: ' . $e->getMessage(), [
                'request' => $request->all()
            ]);

            $returnData['RspCode'] = '99';
            $returnData['Message'] = 'Unknown error';
        }

        return response()->json($returnData);
    }

    private function getVNPayErrorMessage($responseCode)
    {
        $errorMessages = [
            '07' => 'Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường).',
            '09' => 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng.',
            '10' => 'Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần',
            '11' => 'Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch.',
            '12' => 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa.',
            '13' => 'Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP).',
            '24' => 'Giao dịch không thành công do: Khách hàng hủy giao dịch',
            '51' => 'Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch.',
            '65' => 'Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày.',
            '75' => 'Ngân hàng thanh toán đang bảo trì.',
            '79' => 'Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định.',
            '99' => 'Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)'
        ];

        return $errorMessages[$responseCode] ?? 'Giao dịch thất bại';
    }

    public function refund(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:market_orders,id',
            'amount' => 'required|numeric|min:0',
            'reason' => 'required|string|max:255'
        ]);



        return response()->json([
            'success' => true,
            'message' => 'Yêu cầu hoàn tiền đã được gửi'
        ]);
    }
}

