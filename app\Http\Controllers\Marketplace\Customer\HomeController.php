<?php

namespace App\Http\Controllers\Marketplace\Customer;

use App\Http\Controllers\Controller;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Product;
use App\Models\MarketProductReview;

class HomeController extends Controller
{
    public function index()
    {

        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {

                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();


                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }


                $category->products_count = $directProductCount + $childProductCount;

                return $category;
            })
            ->sortByDesc('products_count')
            ->values();


        $topCategories = $allParentCategories->take(4)->values();
        $moreCategories = $allParentCategories->slice(5)->values()->all();


        $featuredProducts = Product::where('status', true)
            ->where('is_featured', true)
            ->with(['category'])
            ->orderBy('created_at', 'desc')
            ->take(8)
            ->get()
            ->map(function($product) {
                $reviews = MarketProductReview::where('product_id', $product->id)->get();
                $totalReviews = $reviews->count();
                $averageRating = $totalReviews > 0 ? $reviews->avg('rating') : 0;

                $product->rating = round($averageRating, 1);
                $product->review_count = $totalReviews;

                return $product;
            });


        $newArrivals = Product::where('status', true)
            ->with(['category'])
            ->orderBy('created_at', 'desc')
            ->take(8)
            ->get()
            ->map(function($product) {
                $reviews = MarketProductReview::where('product_id', $product->id)->get();
                $totalReviews = $reviews->count();
                $averageRating = $totalReviews > 0 ? $reviews->avg('rating') : 0;

                $product->rating = round($averageRating, 1);
                $product->review_count = $totalReviews;

                return $product;
            });


        $brands = Product::where('status', true)
            ->whereNotNull('brand')
            ->where('brand', '!=', '')
            ->select('brand')
            ->distinct()
            ->take(6)
            ->pluck('brand')
            ->map(function($brand) {
                return [
                    'id' => md5($brand),
                    'name' => $brand,
                    'image' => "https://picsum.photos/200/100?random=" . rand(61, 70)
                ];
            })
            ->toArray();

        return Inertia::render('Marketplace/Public/Home/Home', [
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
            'featuredProducts' => $featuredProducts,
            'newArrivals' => $newArrivals,
            'brands' => $brands,
            'csrfToken' => csrf_token(),
        ]);
    }
}
