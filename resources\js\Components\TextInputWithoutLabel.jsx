import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';

export default forwardRef(function TextInputWithoutLabel(
    {
        type = 'text',
        className = '',
        isFocused = false,
        placeholder,
        id,
        errors,
        leftIcon,
        rightIcon,
        ...props
    },
    ref,
) {
    const localRef = useRef(null);

    useImperativeHandle(ref, () => ({
        focus: () => localRef.current?.focus(),
    }));

    useEffect(() => {
        if (isFocused) {
            localRef.current?.focus();
        }
    }, [isFocused]);

    return (
        <div className="mb-4">
            <div className="relative">
                {leftIcon && (
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-500">
                        {leftIcon}
                    </div>
                )}
                <input
                    {...props}
                    type={type}
                    id={id}
                    ref={localRef}
                    className={`w-full border rounded-md text-sm focus:outline-none focus:ring-2 
                        ${errors ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-600'}
                        ${leftIcon ? 'pl-10' : 'px-4'} 
                        ${rightIcon ? 'pr-10' : 'px-4'}
                        py-2 h-10
                        ${className}`}
                    placeholder={placeholder}
                />
                {rightIcon && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-gray-500">
                        {rightIcon}
                    </div>
                )}
            </div>
            {errors && (
                <p className="mt-1 text-sm text-red-600">{errors}</p>
            )}
        </div>
    );
}); 