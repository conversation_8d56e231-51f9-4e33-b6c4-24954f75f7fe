import { router } from "@inertiajs/react";

export default function PrimaryButton({
    className = '',
    href = null,
    disabled,
    children,
    ...props
}) {
    const handleNavigate = () => {
        if (href) {
            router.visit(href);
        }
    }
    return (
        <button
            {...props}
            className={
                `inline-flex items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-xs font-semibold tracking-widest text-white transition duration-150 ease-in-out hover:bg-primary-500  active:bg-primary-900 ${disabled && 'opacity-25'
                } ` + className
            }
            disabled={disabled}
            onClick={href ? handleNavigate : props.onClick}
        >
            {children}
        </button>
    );
}