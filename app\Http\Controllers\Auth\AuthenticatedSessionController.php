<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): Response
    {
        return Inertia::render('Auth/Login', [
            'canResetPassword' => Route::has('password.request'),
            'status' => session('status'),
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        try {
            $request->authenticate();
            $request->session()->regenerate();
            $user = Auth::user();
            $redirectUrl = $request->query('redirect');

            if ($user->hasAnyRole(['super-admin', 'admin', 'manager', 'staff'])) {
                return redirect()->intended(route('dashboard', absolute: false));
            }
            if ($redirectUrl) {
                return redirect()->intended($redirectUrl)
                    ->with('flash.success', 'Đăng nhập thành công !');
            }
            return redirect()->intended(route('about', absolute: false))
                ->with('flash.success', 'Đăng nhập thành công !');
        } catch (\Exception $e) {
            Log::error('Authentication error: ' . $e->getMessage());
            throw $e;
        }
    }


    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
