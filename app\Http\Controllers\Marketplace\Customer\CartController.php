<?php

namespace App\Http\Controllers\Marketplace\Customer;

use App\Http\Controllers\Controller;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Models\Cart;
use App\Models\Product;
use App\Models\Category;
use App\Models\PaymentMethod;
use App\Services\SystemSettingService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class CartController extends Controller
{
    public function index()
    {
        $cartItems = [];
        $cartCount = 0;

        if (Auth::check()) {
            $cartItems = Cart::where('user_id', Auth::id())
                ->with(['product' => function($query) {
                    $query->with('category');
                }])
                ->get()
                ->map(function($cart) {
                    return [
                        'id' => $cart->id,
                        'product_id' => $cart->product_id,
                        'name' => $cart->product->name,
                        'slug' => $cart->product->slug,
                        'image' => $cart->product->image_url_formatted ?? $cart->product->image_url,
                        'price' => $cart->price,
                        'current_price' => $cart->product->sale_price,
                        'quantity' => $cart->quantity,
                        'category' => $cart->product->category ? $cart->product->category->name : '',
                        'stock_quantity' => $cart->product->quantity,
                        'subtotal' => $cart->quantity * $cart->price,
                        'status' => $cart->product->status,
                        'is_available' => $cart->product->status && $cart->product->quantity > 0,
                    ];
                });
            $cartCount = $cartItems->count();
        }


        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {
                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();

                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }

                $category->products_count = $directProductCount + $childProductCount;
                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();


        $paymentMethods = $this->getEnabledPaymentMethods();

        return Inertia::render('Marketplace/Public/Cart/Cart', [
            'cartItems' => $cartItems,
            'cartCount' => $cartCount,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
            'paymentMethods' => $paymentMethods,
            'csrf_token' => csrf_token(),
        ]);
    }

    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
        ]);


        $userId = Auth::id();
        $productId = $request->product_id;
        $cacheKey = "cart_add_{$userId}_{$productId}";

        if (Cache::has($cacheKey)) {
            return response()->json(['error' => 'Yêu cầu đang được xử lý'], 429);
        }


        Cache::put($cacheKey, true, 2);

        try {
            $product = Product::findOrFail($request->product_id);

            if (!$product->status) {
                return response()->json(['error' => 'Sản phẩm không khả dụng'], 400);
            }

            if ($request->quantity > $product->quantity) {
                return response()->json(['error' => 'Số lượng vượt quá tồn kho'], 400);
            }


            $quantityToAdd = 1;

            if (Auth::check()) {
                $cart = Cart::where('user_id', Auth::id())
                    ->where('product_id', $request->product_id)
                    ->first();

                if ($cart) {
                    $newQuantity = $cart->quantity + $quantityToAdd;
                    if ($newQuantity > $product->quantity) {
                        return response()->json(['error' => 'Số lượng vượt quá tồn kho'], 400);
                    }
                    $cart->update(['quantity' => $newQuantity]);
                } else {
                    Cart::create([
                        'user_id' => Auth::id(),
                        'product_id' => $request->product_id,
                        'quantity' => $quantityToAdd,
                        'price' => $product->sale_price,
                    ]);
                }

                $cartCount = Cart::where('user_id', Auth::id())->count();
            } else {
                $cartCount = 0;
            }

            return response()->json([
                'success' => true,
                'message' => 'Đã thêm vào giỏ hàng',
                'cart_count' => $cartCount
            ]);
        } finally {

            Cache::forget($cacheKey);
        }
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1',
        ]);

        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $cart = Cart::where('user_id', Auth::id())->findOrFail($id);
        $product = $cart->product;

        if ($request->quantity > $product->quantity) {
            return response()->json(['error' => 'Số lượng vượt quá tồn kho'], 400);
        }

        $cart->update(['quantity' => $request->quantity]);

        return response()->json([
            'success' => true,
            'message' => 'Đã cập nhật giỏ hàng'
        ]);
    }

    public function remove($id)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $cart = Cart::where('user_id', Auth::id())->findOrFail($id);
        $cart->delete();

        $cartCount = Cart::where('user_id', Auth::id())->count();

        return response()->json([
            'success' => true,
            'message' => 'Đã xóa khỏi giỏ hàng',
            'cart_count' => $cartCount
        ]);
    }

    public function getCount()
    {
        if (Auth::check()) {
            $count = Cart::where('user_id', Auth::id())->count();
        } else {
            $count = 0;
        }

        return response()->json(['count' => $count]);
    }

    public function syncFromStorage(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'items' => 'required|array',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        foreach ($request->items as $item) {
            $product = Product::find($item['product_id']);
            if (!$product || !$product->status) continue;

            $cart = Cart::where('user_id', Auth::id())
                ->where('product_id', $item['product_id'])
                ->first();

            if ($cart) {
                $cart->update([
                    'quantity' => min($item['quantity'], $product->quantity),
                    'price' => $product->sale_price
                ]);
            } else {
                Cart::create([
                    'user_id' => Auth::id(),
                    'product_id' => $item['product_id'],
                    'quantity' => min($item['quantity'], $product->quantity),
                    'price' => $product->sale_price,
                ]);
            }
        }

        $cartCount = Cart::where('user_id', Auth::id())->count();

        return response()->json([
            'success' => true,
            'message' => 'Đã đồng bộ giỏ hàng',
            'cart_count' => $cartCount
        ]);
    }

    public function removeSelected(Request $request)
    {
        $request->validate([
            'item_ids' => 'required|array',
            'item_ids.*' => 'required|integer'
        ]);

        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $deletedCount = Cart::where('user_id', Auth::id())
            ->whereIn('id', $request->item_ids)
            ->delete();

        $cartCount = Cart::where('user_id', Auth::id())->count();

        return response()->json([
            'success' => true,
            'message' => "Đã xóa {$deletedCount} sản phẩm khỏi giỏ hàng",
            'cart_count' => $cartCount
        ]);
    }

    public function updateSelected(Request $request)
    {
        $request->validate([
            'updates' => 'required|array',
            'updates.*.id' => 'required|integer',
            'updates.*.quantity' => 'required|integer|min:1'
        ]);

        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $errors = [];
        $updated = 0;

        foreach ($request->updates as $update) {
            $cart = Cart::where('user_id', Auth::id())->find($update['id']);
            if (!$cart) {
                $errors[] = "Cart item {$update['id']} not found";
                continue;
            }

            $product = $cart->product;
            if ($update['quantity'] > $product->quantity) {
                $errors[] = "Số lượng vượt quá tồn kho cho sản phẩm: {$product->name}";
                continue;
            }

            $cart->update(['quantity' => $update['quantity']]);
            $updated++;
        }

        return response()->json([
            'success' => true,
            'message' => "Đã cập nhật {$updated} sản phẩm",
            'errors' => $errors
        ]);
    }

    private function getEnabledPaymentMethods()
    {
        $methods = [];

        $cashSettings = SystemSettingService::get('market_cash_payment', ['active' => false]);
        if ($cashSettings['active'] ?? false) {
            $logoUrl = PaymentMethod::where('payment_code', 'cash')->value('logo_url');
            $methods[] = [
                'id' => 'cash',
                'payment_code' => 'cash',
                'payment_name' => 'Thanh toán khi nhận hàng (COD)',
                'description' => 'Thanh toán bằng tiền mặt khi nhận hàng',
                'logo_url' => $this->getValidLogoUrl($logoUrl),
                'icon' => 'Banknote',
                'provider' => 'cash'
            ];
        }

        $momoSettings = SystemSettingService::get('market_momo_payment', ['active' => false]);
        if ($momoSettings['active'] ?? false) {
            $logoUrl = PaymentMethod::where('payment_code', 'momo')->value('logo_url');
            $methods[] = [
                'id' => 'momo',
                'payment_code' => 'momo',
                'payment_name' => 'Ví điện tử MoMo',
                'description' => 'Thanh toán qua ví điện tử MoMo',
                'logo_url' => $this->getValidLogoUrl($logoUrl),
                'icon' => 'Smartphone',
                'provider' => 'momo'
            ];
        }

        $vnpaySettings = SystemSettingService::get('market_vnpay_payment', ['active' => false]);
        if ($vnpaySettings['active'] ?? false) {
            $logoUrl = PaymentMethod::where('payment_code', 'vnpay')->value('logo_url');
            $methods[] = [
                'id' => 'vnpay',
                'payment_code' => 'vnpay',
                'payment_name' => 'VNPay',
                'description' => 'Thanh toán qua cổng VNPay',
                'logo_url' => $this->getValidLogoUrl($logoUrl),
                'icon' => 'CreditCard',
                'provider' => 'vnpay'
            ];
        }

        $bankSettings = SystemSettingService::get('market_bank_transfer', ['active' => false]);
        if ($bankSettings['active'] ?? false) {
            $logoUrl = PaymentMethod::where('payment_code', 'bank_transfer')->value('logo_url');
            $methods[] = [
                'id' => 'bank_transfer',
                'payment_code' => 'bank_transfer',
                'payment_name' => 'Chuyển khoản ngân hàng',
                'description' => 'Chuyển khoản trực tiếp đến tài khoản ngân hàng',
                'logo_url' => $this->getValidLogoUrl($logoUrl),
                'icon' => 'Building2',
                'provider' => 'bank_transfer'
            ];
        }

        return $methods;
    }

    private function getValidLogoUrl($logoUrl)
    {
        if (!$logoUrl) {
            return null;
        }

        $fullPath = public_path('storage/' . ltrim($logoUrl, '/'));

        if (!file_exists($fullPath) || !is_readable($fullPath)) {
            return null;
        }

        return '/storage/' . ltrim($logoUrl, '/');
    }
}
