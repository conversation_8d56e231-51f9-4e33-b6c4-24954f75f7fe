<?php

namespace App\Http\Controllers;

use App\Http\Controllers\SuperAdmin\StatisticController;
use App\Http\Controllers\Business\DashboardController as BusinessDashboardController;
use App\Http\Controllers\Branch\DashboardController as BranchDashboardController;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Statistic;
use App\Models\User;
use App\Models\Business;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\Branch;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        if ($user) {
            if ($user->getRoleNames()->contains('super-admin')) {
                $fromDate = $request->input('from_date');
                $toDate = $request->input('to_date');
                if ($fromDate && $toDate) {
                    $statisticController = new StatisticController();
                    $stats = $statisticController->getCustomRangeStats($fromDate, $toDate);
                    return Inertia::render('SuperAdmin/Dashboard', [
                        'stats' => $stats,
                        'is_custom_range' => true,
                        'custom_range' => [
                            'from' => $fromDate,
                            'to' => $toDate,
                            'formatted' => Carbon::parse($fromDate)->format('d/m/Y') . ' - ' . Carbon::parse($toDate)->format('d/m/Y')
                        ],
                        'last_updated' => now()->timezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s')
                    ]);
                }

                $period = null;
                $stats = $this->getDashboardStats($request);

                return Inertia::render('SuperAdmin/Dashboard', [
                    'stats' => $stats,
                    'last_updated' => now()->timezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s'),
                    'current_period' => $period
                ]);
            } elseif ($user->getRoleNames()->contains('admin')) {
                $businessDashboardController = new BusinessDashboardController();
                return $businessDashboardController->index($request);

            } elseif ($user->getRoleNames()->contains('manager') || $user->getRoleNames()->contains('staff')) {
                $branchDashboardController = new BranchDashboardController();
                return $branchDashboardController->index($request);
            } else {
                return redirect()->route('welcome');
            }
        }

        return redirect()->route('welcome');
    }


    /**
     * Get dashboard statistics for the SuperAdmin
     *
     * @return array
     */
    public function getDashboardStats(Request $request = null)
    {
        $now = Carbon::now();
        $previousMonth = $now->copy()->subMonth();
        $currentMonth = $now->copy()->startOfMonth();
        $period = $request ? $request->input('period', 'monthly') : 'monthly';

        $totalUsers = User::count();
        $previousMonthUsers = User::where('created_at', '<', $currentMonth)->count();
        $newUsers = $totalUsers - $previousMonthUsers;
        $userGrowth = $previousMonthUsers > 0 ? round(($newUsers / $previousMonthUsers) * 100, 1) : 0;

        $totalBusinesses = Business::count();
        $previousMonthBusinesses = Business::where('created_at', '<', $currentMonth)->count();
        $newBusinesses = $totalBusinesses - $previousMonthBusinesses;
        $businessGrowth = $previousMonthBusinesses > 0 ? round(($newBusinesses / $previousMonthBusinesses) * 100, 1) : 0;

        $totalCourts = Court::count();
        $activeCourts = Court::where('is_active', 1)->count();
        $courtUtilization = $totalCourts > 0 ? round(($activeCourts / $totalCourts) * 100, 1) : 0;

        $totalRevenue = CourtBooking::where('status', 'completed')->sum('total_price');

        $currentMonthRevenue = CourtBooking::where('status', 'completed')
            ->whereYear('booking_date', $now->year)
            ->whereMonth('booking_date', $now->month)
            ->sum('total_price');

        $previousMonthRevenue = CourtBooking::where('status', 'completed')
            ->whereYear('booking_date', $previousMonth->year)
            ->whereMonth('booking_date', $previousMonth->month)
            ->sum('total_price');

        $revenueGrowth = $previousMonthRevenue > 0 ? round((($currentMonthRevenue - $previousMonthRevenue) / $previousMonthRevenue) * 100, 1) : 0;
        $chartData = $this->getChartDataByPeriod($period, $now);
        $labels = $chartData['labels'];
        $counts = $chartData['counts'];
        $revenue = $chartData['revenue'];

        $weeklyData = [];
        $startOfMonth = $now->copy()->startOfMonth();
        $endOfMonth = $now->copy()->endOfMonth();

        $daysInMonth = $startOfMonth->daysInMonth;
        $weekSizes = $this->calculateWeekSizes($daysInMonth);

        $currentDate = $startOfMonth->copy();

        for ($weekIndex = 0; $weekIndex < count($weekSizes); $weekIndex++) {
            $weekSize = $weekSizes[$weekIndex];

            $weekStart = $currentDate->copy();
            $weekEnd = $currentDate->copy()->addDays($weekSize - 1);

            if ($weekEnd->gt($endOfMonth)) {
                $weekEnd = $endOfMonth->copy();
            }

            $weeklyBookings = CourtBooking::where('status', 'completed')
                ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->count();

            $weeklyRevenue = CourtBooking::where('status', 'completed')
                ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->sum('total_price');

            $weeklyData[] = [
                'week' => __('dashboard.week') . ' ' . ($weekIndex + 1),
                'start_date' => $weekStart->format('d/m/Y'),
                'end_date' => $weekEnd->format('d/m/Y'),
                'bookings' => $weeklyBookings,
                'revenue' => $weeklyRevenue,
                'date_range' => $weekStart->format('d/m') . ' - ' . $weekEnd->format('d/m/Y')
            ];

            $currentDate->addDays($weekSize);
        }

        $topBusinesses = Business::select('businesses.id', 'businesses.name')
            ->join('branches', 'businesses.id', '=', 'branches.business_id')
            ->join('court_bookings', 'branches.id', '=', 'court_bookings.branch_id')
            ->where('court_bookings.status', 'completed')
            ->groupBy('businesses.id', 'businesses.name')
            ->orderByRaw('COUNT(DISTINCT court_bookings.reference_number) DESC')
            ->limit(10)
            ->get()
            ->map(function ($business) {
                $bookingCount = CourtBooking::whereHas('branch', function ($q) use ($business) {
                    $q->where('business_id', $business->id);
                })
                    ->where('status', 'completed')
                    ->distinct('reference_number')
                    ->count('reference_number');

                return [
                    'name' => $business->name,
                    'bookings' => $bookingCount
                ];
            });

        $totalRevenueFormatted = number_format($totalRevenue / 1000000, 1) . 'M';

        return [
            'users' => [
                'total' => $totalUsers,
                'growth' => $userGrowth
            ],
            'businesses' => [
                'total' => $totalBusinesses,
                'growth' => $businessGrowth
            ],
            'courts' => [
                'total' => $totalCourts,
                'active' => $activeCourts,
                'utilization' => $courtUtilization
            ],
            'revenue' => [
                'total' => $totalRevenue,
                'formatted' => $totalRevenueFormatted,
                'growth' => $revenueGrowth
            ],
            'chart' => [
                'labels' => $labels,
                'bookings' => $counts,
                'revenue' => $revenue
            ],
            'weekly' => $weeklyData,
            'top_businesses' => $topBusinesses
        ];
    }

    /**
     * Calculate the number of days for each week in a month
     *
     * @param int $daysInMonth
     * @return array
     */
    private function calculateWeekSizes($daysInMonth)
    {
        switch ($daysInMonth) {
            case 28:
                return [7, 7, 7, 7];
            case 29:
                return [8, 7, 7, 7];
            case 30:
                return [8, 7, 8, 7];
            case 31:
            default:
                return [8, 8, 8, 7];
        }
    }

    /**
     * Get chart data based on selected period
     *
     * @param string $period
     * @param Carbon $now
     * @return array
     */
    private function getChartDataByPeriod($period, Carbon $now)
    {
        $labels = [];
        $counts = [];
        $revenue = [];

        switch ($period) {
            case 'daily':
                for ($i = 23; $i >= 0; $i--) {
                    $hour = $now->copy()->subHours($i);
                    $hourStart = $hour->copy()->startOfHour();
                    $hourEnd = $hour->copy()->endOfHour();

                    $labels[] = $hour->format('H:i');

                    $count = CourtBooking::where('status', 'completed')
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->count();

                    $rev = CourtBooking::where('status', 'completed')
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'weekly':
                for ($i = 6; $i >= 0; $i--) {
                    $day = $now->copy()->subDays($i);
                    $labels[] = $day->format('D');

                    $count = CourtBooking::where('status', 'completed')
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->count();

                    $rev = CourtBooking::where('status', 'completed')
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'yearly':
                for ($i = 11; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M Y');

                    $count = CourtBooking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->count();

                    $rev = CourtBooking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'monthly':
            default:
                for ($i = 5; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M');

                    $count = CourtBooking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->count();

                    $rev = CourtBooking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;
        }

        return [
            'labels' => $labels,
            'counts' => $counts,
            'revenue' => $revenue
        ];
    }
}
